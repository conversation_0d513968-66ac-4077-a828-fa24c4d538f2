from fastapi import APIRouter, BackgroundTasks, Depends, File, Query, Request, UploadFile, Body, Form
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.common_service import CommonService
from module_admin.service.voice_service import VoiceService
from module_admin.service.custom_mini_qr_service import CustomMiniQrService
from module_admin.service.internal_user_service import InternalUserService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.share_mapping_service import ShareMappingService
from module_admin.service.file_service import FileService
from module_admin.service.store_service import StoreService
from module_admin.service.wechat_official_service import WechatOfficialService
from module_admin.entity.vo.common_vo import CustomMiniQrRequestModel, CustomMiniQrResponseModel
from module_admin.entity.vo.internal_user_vo import DashboardInitResponse, CurrentInternalUserModel
from module_admin.entity.vo.wechat_official_vo import (
    GenerateWechatQrCodeRequest,
    GenerateWechatQrCodeResponse,
    WechatBindStatusResponse
)
from utils.wechat_miniprogram_util import WechatMiniprogramUtil
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import DatabaseException, BusinessException, ResourceNotFoundException
from config.enums import RedisInitKeyConfig
import os
import tempfile
import aiohttp
from pydantic import BaseModel
from typing import Optional

# 使用统一的API路由前缀格式
commonController = APIRouter(prefix='/api/v1/common')


@commonController.post('/upload', summary="文件上传接口")
async def common_upload(
    file: UploadFile = File(...),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """文件上传接口

    上传文件到华为云OBS并返回文件访问URL和相关信息

    - **file**: 要上传的文件
    """
    # 从当前用户信息中获取用户名
    user_name = current_user.user.name or 'unknown'

    upload_result = await CommonService.upload_service(file, None, user_name)
    logger.info('上传成功')

    return ResponseUtil.success(data=upload_result)


@commonController.get('/download')
async def common_download(
    request: Request,
    background_tasks: BackgroundTasks,
    file_name: str = Query(alias='fileName'),
    delete: bool = Query(),
):
    download_result = await CommonService.download_services(background_tasks, file_name, delete)
    logger.info(download_result.message)

    return ResponseUtil.streaming(data=download_result.result)


@commonController.get('/download/resource')
async def common_download_resource(request: Request, resource: str = Query()):
    download_resource_result = await CommonService.download_resource_services(resource)
    logger.info(download_resource_result.message)

    return ResponseUtil.streaming(data=download_resource_result.result)


@commonController.get('/file/list/{main_id}', summary="根据文件主表ID获取图集")
async def get_file_list_by_main_id(
    main_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    根据文件主表ID获取图集接口

    根据文件主表ID获取该组下的所有文件列表，主要用于获取产品图集

    Args:
        main_id: 文件主表ID
        db: 数据库会话

    Returns:
        文件列表，包含文件URL、文件名等信息
    """
    try:
        # 调用文件服务获取文件列表
        file_list = await FileService.get_file_list_by_main_id(db, main_id)

        logger.info(f"获取文件列表成功，主表ID: {main_id}，文件数量: {len(file_list)}")
        return ResponseUtil.success(
            msg="获取文件列表成功",
            data=file_list
        )
    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}")
        return ResponseUtil.error(f"获取文件列表失败: {str(e)}")


@commonController.get('/file/download/{file_id}', summary="根据文件ID下载文件（公开接口）")
async def common_file_download(
    file_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    根据文件ID下载文件接口（公开接口，无需认证）

    根据文件主表ID获取文件URL并重定向到实际文件地址

    Args:
        file_id: 文件主表ID
        db: 数据库会话

    Returns:
        重定向到文件URL或返回错误信息
    """
    try:
        from module_admin.service.file_service import FileService
        from fastapi.responses import RedirectResponse

        logger.info(f"文件下载请求，文件ID: {file_id}")

        # 调用文件服务获取文件列表
        file_list = await FileService.get_file_list_by_main_id(db, int(file_id))

        if not file_list:
            logger.warning(f"文件不存在，文件ID: {file_id}")
            return ResponseUtil.error(msg="文件不存在", code=404)

        # 获取第一个文件的URL
        file_url = file_list[0].get("url", "")

        if not file_url:
            logger.warning(f"文件URL为空，文件ID: {file_id}")
            return ResponseUtil.error(msg="文件URL无效", code=404)

        logger.info(f"文件下载成功，重定向到: {file_url}")

        # 重定向到实际的文件URL
        return RedirectResponse(url=file_url, status_code=302)

    except ValueError:
        logger.warning(f"无效的文件ID格式: {file_id}")
        return ResponseUtil.error(msg="无效的文件ID格式", code=400)
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        return ResponseUtil.error(msg=f"文件下载失败: {str(e)}")


@commonController.get('/store/getMiniQrCode', summary="获取门店小程序码")
async def get_store_mini_qr_code(
    store_uuid: str = Query(..., description="门店UUID"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取门店小程序码接口

    先查询数据库中是否已有小程序码，如果没有则自动生成并保存

    Args:
        store_uuid: 门店UUID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        包含小程序码URL的响应数据
    """
    try:
        # 调用服务层获取门店小程序码
        result = await StoreService.get_store_mini_qr_code(db, store_uuid)

        logger.info(f"获取门店小程序码成功: {store_uuid}")
        return ResponseUtil.success(
            msg="获取门店小程序码成功",
            data=result
        )
    except DatabaseException as e:
        logger.error(f"获取门店小程序码数据库异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取门店小程序码业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"门店资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取门店小程序码异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@commonController.post('/generateCustomMiniQr')
async def generate_custom_mini_qr(
    request: Request,
    data: CustomMiniQrRequestModel = Body(...)
) -> CustomMiniQrResponseModel:
    """
    生成自定义小程序二维码

    支持传入自定义的小程序AppID和AppSecret来生成其他小程序的二维码
    """
    try:
        # 调用服务层生成自定义小程序二维码
        result = await CustomMiniQrService.generate_custom_mini_qr(data)
        logger.info("生成自定义小程序二维码成功")
        return result
    except Exception as e:
        logger.error(f"生成自定义小程序二维码接口异常: {str(e)}", exc_info=True)
        return CustomMiniQrResponseModel(
            code=1,
            msg=f"生成自定义小程序二维码失败: {str(e)}",
            data={"qr_url": ""}
        )


@commonController.get('/dashboard-init', response_model=DashboardInitResponse, summary='获取首页初始化数据')
async def get_dashboard_init_data(
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取首页初始化数据"""
    result = await InternalUserService.get_dashboard_init_data(query_db, current_user)
    return ResponseUtil.success(model_content=result)


@commonController.post('/voice/recognize', summary="语音识别接口")
async def voice_recognize(
    audio: UploadFile = File(..., description="音频文件"),
    duration: int = Form(default=0, description="录音时长（秒）"),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    语音识别接口

    使用百度语音识别服务将音频文件转换为文字

    Args:
        audio: 音频文件
        duration: 录音时长
        current_user: 当前用户

    Returns:
        识别结果
    """
    try:
        # 获取用户信息
        user_uuid = getattr(current_user.user, 'uuid', None)
        logger.info(f"用户 {user_uuid} 请求语音识别，录音时长: {duration}秒")

        # 检查文件类型
        if not audio.content_type or not audio.content_type.startswith('audio/'):
            return ResponseUtil.failure("请上传音频文件")

        # 检查文件大小（限制10MB）
        if audio.size and audio.size > 10 * 1024 * 1024:
            return ResponseUtil.failure("音频文件过大，请限制在10MB以内")

        # 创建临时文件保存上传的音频
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_file:
            content = await audio.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # 调用语音识别服务
            voice_service = VoiceService()
            recognition_result = await voice_service.recognize_audio(temp_file_path)

            if recognition_result and recognition_result.get('text'):
                logger.info(f"语音识别成功，结果: {recognition_result['text']}")
                return ResponseUtil.success({
                    'text': recognition_result['text'],
                    'confidence': recognition_result.get('confidence', 0),
                    'duration': duration
                })
            else:
                logger.warning("语音识别结果为空")
                return ResponseUtil.failure("未识别到语音内容，请确保录音清晰")

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

    except Exception as e:
        logger.error(f"语音识别失败: {e}")
        return ResponseUtil.error("语音识别服务异常")


# 定义语音识别请求模型
class VoiceRecognizeByUrlRequest(BaseModel):
    fileUrl: str
    fileName: str = None
    duration: int = 0


@commonController.post('/voice/recognize-by-url', summary="通过文件URL进行语音识别")
async def voice_recognize_by_url(
    request: VoiceRecognizeByUrlRequest,
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    通过文件URL进行语音识别接口

    使用百度语音识别服务将已上传的音频文件转换为文字

    Args:
        request: 包含文件URL和相关信息的请求对象
        current_user: 当前用户

    Returns:
        识别结果
    """
    try:
        # 获取用户信息
        user_uuid = getattr(current_user.user, 'uuid', None)
        logger.info(f"用户 {user_uuid} 请求语音识别，文件URL: {request.fileUrl}")

        # 下载文件到临时目录
        temp_file_path = await download_file_from_url(request.fileUrl)

        try:
            # 调用语音识别服务
            voice_service = VoiceService()
            recognition_result = await voice_service.recognize_audio(temp_file_path)

            if recognition_result and recognition_result.get('text'):
                logger.info(f"语音识别成功，结果: {recognition_result['text']}")
                return ResponseUtil.success({
                    'text': recognition_result['text'],
                    'confidence': recognition_result.get('confidence', 0),
                    'duration': request.duration
                })
            else:
                logger.warning("语音识别结果为空")
                return ResponseUtil.failure("未识别到语音内容，请确保录音清晰")

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

    except Exception as e:
        logger.error(f"语音识别失败: {e}")
        return ResponseUtil.error("语音识别服务异常")


async def download_file_from_url(file_url: str) -> str:
    """
    从URL下载文件到临时目录

    Args:
        file_url: 文件URL

    Returns:
        临时文件路径
    """
    try:
        # 从URL中提取文件扩展名
        import os
        from urllib.parse import urlparse

        parsed_url = urlparse(file_url)
        file_path = parsed_url.path
        _, file_extension = os.path.splitext(file_path)

        # 如果没有扩展名，默认使用.wav
        if not file_extension:
            file_extension = '.wav'

        logger.info(f"下载文件，检测到扩展名: {file_extension}")

        async with aiohttp.ClientSession() as session:
            async with session.get(file_url) as response:
                if response.status == 200:
                    # 创建临时文件，使用正确的扩展名
                    with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                        content = await response.read()
                        temp_file.write(content)
                        logger.info(f"文件下载成功，临时文件路径: {temp_file.name}")
                        return temp_file.name
                else:
                    raise Exception(f"下载文件失败，HTTP状态码: {response.status}")
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        raise Exception(f"下载文件失败: {str(e)}")


# 分享映射相关的请求模型
class CreateShareMappingRequest(BaseModel):
    user_uuid: str
    store_id: str
    expire_days: int = 365


class ShareMappingResponse(BaseModel):
    short_code: str


# 地理编码相关的请求模型
class GeocodeRequest(BaseModel):
    address: str


# 逆地理编码请求模型
class ReverseGeocodeRequest(BaseModel):
    latitude: float
    longitude: float


class GeocodeLocationModel(BaseModel):
    lat: float
    lng: float


class GeocodeFormattedAddressesModel(BaseModel):
    recommend: Optional[str] = None
    rough: Optional[str] = None


class GeocodeResultModel(BaseModel):
    location: GeocodeLocationModel
    formatted_addresses: Optional[GeocodeFormattedAddressesModel] = None
    address_components: Optional[dict] = None


class GeocodeResponse(BaseModel):
    status: int
    message: str
    result: Optional[GeocodeResultModel] = None


@commonController.post('/share/create-mapping', response_model=ShareMappingResponse, summary="创建分享映射")
async def create_share_mapping(
    request: CreateShareMappingRequest,
    current_user = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建分享映射，生成短码用于小程序分享

    Args:
        request: 包含用户UUID和门店ID的请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        包含短码的响应
    """
    try:
        # 生成短码映射
        short_code = await ShareMappingService.create_share_mapping(
            db, request.user_uuid, request.store_id, request.expire_days
        )

        logger.info(f"创建分享映射成功: {short_code}")
        return ResponseUtil.success({
            "short_code": short_code
        })

    except Exception as e:
        logger.error(f"创建分享映射失败: {e}")
        return ResponseUtil.error(f"创建分享映射失败: {str(e)}")


@commonController.get('/share/parse-mapping/{short_code}', summary="解析分享映射")
async def parse_share_mapping(
    short_code: str,
    db: AsyncSession = Depends(get_db)
):
    """
    根据短码解析分享映射信息

    Args:
        short_code: 8位短码
        db: 数据库会话

    Returns:
        包含用户UUID和门店ID的信息
    """
    try:
        # 解析短码映射
        mapping_info = await ShareMappingService.get_share_mapping(db, short_code)

        if not mapping_info:
            return ResponseUtil.failure("短码不存在或已过期")

        logger.info(f"解析分享映射成功: {short_code} -> {mapping_info}")
        return ResponseUtil.success(mapping_info)

    except Exception as e:
        logger.error(f"解析分享映射失败: {e}")
        return ResponseUtil.error(f"解析分享映射失败: {str(e)}")


@commonController.get('/share/stats', summary="获取用户分享统计")
async def get_share_stats(
    current_user = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的分享统计信息

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        分享统计信息
    """
    try:
        # 获取用户UUID
        user_uuid = getattr(current_user.user, 'uuid', None)
        if not user_uuid:
            return ResponseUtil.failure("用户信息不完整")

        # 获取分享统计
        stats = await ShareMappingService.get_user_share_stats(db, user_uuid)

        logger.info(f"获取用户分享统计成功: {user_uuid}")
        return ResponseUtil.success(stats)

    except Exception as e:
        logger.error(f"获取分享统计失败: {e}")
        return ResponseUtil.error(f"获取分享统计失败: {str(e)}")


@commonController.post('/geocode', response_model=GeocodeResponse, summary="地理编码")
async def geocode_address(
    geocode_request: GeocodeRequest,
    request: Request,
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    地理编码接口 - 将地址转换为经纬度坐标

    Args:
        request: 包含地址的请求
        current_user: 当前用户

    Returns:
        包含经纬度坐标的响应
    """
    try:
        # 腾讯地图API Key
        map_key = "VG5BZ-IQQCU-XCNVG-4IWN3-M3UFV-AEBQG"

        # 构建请求URL
        url = f"https://apis.map.qq.com/ws/geocoder/v1/?address={geocode_request.address}&key={map_key}"

        # 发起HTTP请求 - 跳过SSL证书验证
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()

                    logger.info(f"腾讯地图API响应: {data}")

                    if data.get('status') == 0 and data.get('result'):
                        # 构建响应数据
                        result_data = data['result']
                        location = result_data.get('location', {})
                        formatted_addresses = result_data.get('formatted_addresses', {})

                        geocode_result = GeocodeResultModel(
                            location=GeocodeLocationModel(
                                lat=location.get('lat', 0),
                                lng=location.get('lng', 0)
                            ),
                            formatted_addresses=GeocodeFormattedAddressesModel(
                                recommend=formatted_addresses.get('recommend'),
                                rough=formatted_addresses.get('rough')
                            ) if formatted_addresses else None,
                            address_components=result_data.get('address_components')
                        )

                        response_data = GeocodeResponse(
                            status=0,
                            message="地理编码成功",
                            result=geocode_result
                        )

                        logger.info(f"地理编码成功: {geocode_request.address} -> {location}")
                        return ResponseUtil.success(data=response_data.model_dump(), msg="地理编码成功")
                    else:
                        # 处理API错误
                        error_message = data.get('message', '未知错误')
                        status_code = data.get('status', -1)

                        # 特殊错误处理
                        if status_code == 112 or 'IP未被授权' in error_message:
                            error_message = '地图服务配置问题，请联系管理员处理IP授权'
                        elif status_code == 110 or '请求来源未被授权' in error_message:
                            error_message = '域名未授权，请在腾讯地图控制台添加域名白名单'
                        elif '调用量已达到上限' in error_message:
                            error_message = '地图服务调用量已达上限，请稍后重试'

                        logger.error(f"腾讯地图API错误: {error_message}")
                        return ResponseUtil.failure(f"地理编码失败: {error_message}")
                else:
                    logger.error(f"HTTP请求失败: {response.status}")
                    return ResponseUtil.failure(f"网络请求失败: HTTP {response.status}")

    except aiohttp.ClientTimeout:
        logger.error("地理编码请求超时")
        return ResponseUtil.failure("地理编码请求超时，请重试")
    except Exception as e:
        logger.error(f"地理编码失败: {e}")
        return ResponseUtil.error(f"地理编码失败: {str(e)}")


@commonController.post('/reverse-geocode', response_model=GeocodeResponse, summary="逆地理编码")
async def reverse_geocode_location(
    reverse_geocode_request: ReverseGeocodeRequest,
    request: Request,
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    逆地理编码接口 - 将经纬度坐标转换为地址信息

    Args:
        reverse_geocode_request: 包含经纬度的请求
        current_user: 当前用户

    Returns:
        包含地址信息的响应
    """
    try:
        # 腾讯地图API Key
        map_key = "VG5BZ-IQQCU-XCNVG-4IWN3-M3UFV-AEBQG"

        # 构建请求URL - 逆地理编码
        location = f"{reverse_geocode_request.latitude},{reverse_geocode_request.longitude}"
        url = f"https://apis.map.qq.com/ws/geocoder/v1/?location={location}&key={map_key}"

        logger.info(f"逆地理编码请求: {location}")

        # 发起HTTP请求 - 跳过SSL证书验证
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()

                    logger.info(f"腾讯地图逆地理编码API响应: {data}")

                    if data.get('status') == 0 and data.get('result'):
                        # 构建响应数据
                        result_data = data['result']
                        location_data = result_data.get('location', {})
                        formatted_addresses = result_data.get('formatted_addresses', {})

                        geocode_result = GeocodeResultModel(
                            location=GeocodeLocationModel(
                                lat=location_data.get('lat', reverse_geocode_request.latitude),
                                lng=location_data.get('lng', reverse_geocode_request.longitude)
                            ),
                            formatted_addresses=GeocodeFormattedAddressesModel(
                                recommend=formatted_addresses.get('recommend'),
                                rough=formatted_addresses.get('rough')
                            ) if formatted_addresses else None,
                            address_components=result_data.get('address_component')
                        )

                        response_data = GeocodeResponse(
                            status=0,
                            message="逆地理编码成功",
                            result=geocode_result
                        )

                        # 提取城市信息用于日志
                        city = result_data.get('address_component', {}).get('city', '未知')
                        logger.info(f"逆地理编码成功: {location} -> {city}")

                        return ResponseUtil.success(data=response_data.model_dump(), msg="逆地理编码成功")
                    else:
                        # 处理API错误
                        error_message = data.get('message', '未知错误')
                        status_code = data.get('status', -1)

                        # 特殊错误处理
                        if status_code == 112 or 'IP未被授权' in error_message:
                            error_message = '地图服务配置问题，请联系管理员处理IP授权'
                        elif status_code == 110 or '请求来源未被授权' in error_message:
                            error_message = '域名未授权，请在腾讯地图控制台添加域名白名单'
                        elif '调用量已达到上限' in error_message:
                            error_message = '地图服务调用量已达上限，请稍后重试'

                        logger.error(f"腾讯地图逆地理编码API错误: {error_message}")
                        return ResponseUtil.failure(f"逆地理编码失败: {error_message}")
                else:
                    logger.error(f"HTTP请求失败: {response.status}")
                    return ResponseUtil.failure(f"网络请求失败: HTTP {response.status}")

    except Exception as e:
        logger.error(f"逆地理编码失败: {e}")
        return ResponseUtil.error(f"逆地理编码失败: {str(e)}")


# 微信公众号相关接口
@commonController.post('/wechat/generateQrCode', summary="生成微信公众号带参数二维码")
async def generate_wechat_qr_code(
    request: GenerateWechatQrCodeRequest,
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    生成微信公众号带参数二维码

    Args:
        request: 生成二维码请求数据
        current_user: 当前用户

    Returns:
        二维码URL和相关信息
    """
    try:
        logger.info(f"用户 {current_user.user.uuid} 请求生成微信公众号二维码")

        # 调用服务层生成二维码
        result = await WechatOfficialService.generate_qr_code(request)

        if result:
            logger.info(f"生成微信公众号二维码成功")
            return ResponseUtil.success(
                data=result.model_dump(),
                msg="生成二维码成功"
            )
        else:
            logger.error("生成微信公众号二维码失败")
            return ResponseUtil.failure("生成二维码失败，请稍后重试")

    except Exception as e:
        logger.error(f"生成微信公众号二维码异常: {str(e)}")
        return ResponseUtil.error(f"生成二维码失败: {str(e)}")


@commonController.get('/wechat/bindStatus', summary="检查用户微信公众号绑定状态")
async def check_wechat_bind_status(
    current_user = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    检查用户微信公众号绑定状态

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        绑定状态信息
    """
    try:
        user_id = current_user.user.uuid
        logger.info(f"检查用户 {user_id} 微信公众号绑定状态")

        # 调用服务层检查绑定状态
        result = await WechatOfficialService.check_bind_status(db, user_id)

        logger.info(f"用户 {user_id} 微信绑定状态: {result.isBound}")
        return ResponseUtil.success(
            data=result.model_dump(),
            msg="获取绑定状态成功"
        )

    except Exception as e:
        logger.error(f"检查微信绑定状态异常: {str(e)}")
        return ResponseUtil.error(f"检查绑定状态失败: {str(e)}")


@commonController.post('/wechat/unbind', summary="解绑微信公众号")
async def unbind_wechat(
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    解绑微信公众号

    Args:
        current_user: 当前登录用户
        db: 数据库会话

    Returns:
        解绑结果
    """
    try:
        user_id = current_user.user.uuid
        logger.info(f"用户 {user_id} 请求解绑微信公众号")

        # 执行解绑操作
        success = await WechatOfficialService.unbind_wechat(db, user_id)

        if success:
            return ResponseUtil.success(msg="解绑成功")
        else:
            return ResponseUtil.failure(msg="解绑失败，未找到绑定记录")

    except Exception as e:
        logger.error(f"解绑微信公众号异常: {str(e)}")
        return ResponseUtil.error(msg="解绑失败")


@commonController.post('/wechat/sendTestMessage', summary="发送测试推送消息")
async def send_test_message(
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    发送测试推送消息（绑定成功通知）

    Args:
        current_user: 当前登录用户
        db: 数据库会话

    Returns:
        发送结果
    """
    try:
        user_id = current_user.user.uuid
        user_name = current_user.user.name or "用户"
        logger.info(f"用户 {user_id} 请求发送测试推送消息")

        # 发送绑定成功通知作为测试消息
        result = await WechatOfficialService.send_bind_success_notification(
            db, user_id, user_name, "家政服务平台"
        )

        if result.get('success'):
            logger.info(f"测试推送消息发送成功，消息ID: {result.get('message_id')}")
            return ResponseUtil.success(
                data={
                    "message_id": result.get('message_id'),
                    "send_time": result.get('send_time')
                },
                msg="测试消息发送成功"
            )
        else:
            error_msg = result.get('message', '发送失败')
            logger.error(f"测试推送消息发送失败: {error_msg}")
            return ResponseUtil.failure(error_msg)

    except Exception as e:
        logger.error(f"发送测试推送消息异常: {str(e)}")
        return ResponseUtil.error(msg="发送测试消息失败")


@commonController.post('/wechat/sendTemplateMessage', summary="发送模板消息")
async def send_template_message(
    request: Request,
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    发送模板消息通用接口

    Args:
        request: HTTP请求对象
        current_user: 当前登录用户
        db: 数据库会话

    Returns:
        发送结果
    """
    try:
        # 获取请求数据
        request_data = await request.json()

        template_type = request_data.get('templateType')
        template_id = request_data.get('templateId')
        template_data = request_data.get('templateData', {})
        miniprogram = request_data.get('miniprogram')

        user_id = current_user.user.uuid
        logger.info(f"用户 {user_id} 请求发送模板消息，类型: {template_type}")

        # 获取用户微信绑定信息
        from module_admin.dao.wechat_official_dao import WechatOfficialDao
        bind_info = await WechatOfficialDao.get_user_wechat_bind_info(db, user_id)

        if not bind_info or not bind_info.get('wx_official_openid'):
            return ResponseUtil.failure(msg="用户未绑定微信公众号")

        openid = bind_info['wx_official_openid']

        # 发送模板消息
        result = await WechatOfficialService.send_template_message(
            openid=openid,
            template_id=template_id,
            data=template_data,
            miniprogram=miniprogram
        )

        if result.get('success'):
            logger.info(f"模板消息发送成功，消息ID: {result.get('message_id')}")
            return ResponseUtil.success(
                data={
                    "message_id": result.get('message_id'),
                    "send_time": result.get('send_time')
                },
                msg="模板消息发送成功"
            )
        else:
            error_msg = result.get('message', '发送失败')
            logger.error(f"模板消息发送失败: {error_msg}")
            return ResponseUtil.failure(msg=error_msg)

    except Exception as e:
        logger.error(f"发送模板消息异常: {str(e)}")
        return ResponseUtil.error(msg="发送模板消息失败")


# 微信手机号解密请求模型
class DecryptWechatPhoneRequest(BaseModel):
    wx_code: str
    encrypted_data: str
    iv: str


@commonController.post('/decryptWechatPhone', summary="解密微信手机号")
async def decrypt_wechat_phone(
    request: DecryptWechatPhoneRequest
):
    """
    解密微信手机号接口

    用于入驻申请页面获取用户微信手机号

    Args:
        request: 包含微信授权码和加密数据的请求

    Returns:
        解密后的手机号信息
    """
    try:
        logger.info(f"收到微信手机号解密请求，wx_code: {request.wx_code}")

        # 调用微信小程序工具类获取用户信息（包含手机号）
        user_info = await WechatMiniprogramUtil.get_user_info_by_code(
            wx_code=request.wx_code,
            encrypted_data=request.encrypted_data,
            iv=request.iv
        )

        if not user_info:
            logger.error("获取微信用户信息失败")
            return ResponseUtil.failure("获取微信用户信息失败")

        # 提取手机号信息
        phone_number = user_info.get('phoneNumber') or user_info.get('purePhoneNumber')
        pure_phone_number = user_info.get('purePhoneNumber', '')
        country_code = user_info.get('countryCode', '86')

        if not phone_number:
            logger.error("未能从微信获取到手机号")
            return ResponseUtil.failure("未能获取到手机号，请重试")

        # 如果没有纯手机号，从完整手机号中提取
        if not pure_phone_number and phone_number:
            # 移除国家代码前缀，如 +86-13800138000 -> 13800138000
            import re
            pure_phone_number = re.sub(r'^(\+\d{1,3}[-\s]?)', '', phone_number)

        logger.info(f"微信手机号解密成功: {pure_phone_number}")

        return ResponseUtil.success(
            data={
                "phone_number": pure_phone_number,
                "full_phone_number": phone_number,
                "country_code": country_code
            },
            msg="获取手机号成功"
        )

    except Exception as e:
        logger.error(f"微信手机号解密失败: {str(e)}")
        return ResponseUtil.error(f"获取手机号失败: {str(e)}")


@commonController.post('/verifyCode', summary="验证短信验证码")
async def verify_sms_code(
    request: Request,
    mobile: str = Body(..., description="手机号"),
    code: str = Body(..., description="验证码")
):
    """验证短信验证码接口

    验证用户输入的短信验证码是否正确

    Args:
        request: Request对象
        mobile: 手机号
        code: 验证码

    Returns:
        验证结果
    """
    try:
        # 验证手机号格式
        if not mobile or len(mobile) != 11 or not mobile.isdigit():
            return ResponseUtil.failure("手机号格式不正确")

        # 验证验证码格式
        if not code or len(code) != 6 or not code.isdigit():
            return ResponseUtil.failure("验证码格式不正确")

        # 从Redis中获取验证码
        redis_sms_result = await request.app.state.redis.get(f'{RedisInitKeyConfig.SMS_CODE.key}:{mobile}')
        logger.info(f"验证码验证 - 手机号: {mobile}, redis结果: {redis_sms_result}, 用户输入验证码: {code}")

        # 检查验证码是否存在
        if not redis_sms_result:
            return ResponseUtil.failure("验证码已过期，请重新获取")

        # 处理bytes类型的存储值
        redis_sms_result = redis_sms_result.decode() if isinstance(redis_sms_result, bytes) else str(redis_sms_result)

        # 验证验证码是否正确
        if redis_sms_result != str(code):
            logger.warning(f"验证码错误 - 手机号: {mobile}, 输入: {code}, 存储: {redis_sms_result}")
            return ResponseUtil.failure("验证码错误")

        logger.info(f"验证码验证成功 - 手机号: {mobile}")
        return ResponseUtil.success(
            data={"mobile": mobile},
            msg="验证码验证成功"
        )

    except Exception as e:
        logger.error(f"验证码验证异常: {str(e)}")
        return ResponseUtil.error(f"验证码验证失败: {str(e)}")


class CheckEmployeeInfoRequest(BaseModel):
    mobile: str


@commonController.post('/checkEmployeeInfo', summary="检查员工信息")
async def check_employee_info(
    request: CheckEmployeeInfoRequest
):
    """
    检查员工信息接口

    通过手机号查询第三方员工信息系统，获取员工详细信息

    Args:
        request: 包含手机号的请求对象

    Returns:
        成功时返回员工信息，失败时返回空
    """
    try:
        mobile = request.mobile
        logger.info(f"开始检查员工信息，手机号: {mobile}")

        # 调用第三方员工信息API
        api_url = f"https://ortherapi.xiaoyujia.com/xyjemployee/{mobile}"

        # 创建SSL上下文，跳过证书验证
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json'
        }

        connector = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=connector, headers=headers) as session:
            async with session.get(api_url) as response:
                if response.status == 200:
                    # 先获取响应文本
                    response_text = await response.text()
                    logger.info(f"第三方API响应文本: {response_text}")

                    try:
                        # 尝试解析JSON
                        data = await response.json()
                        logger.info(f"第三方API响应JSON: {data}")
                    except Exception as json_error:
                        logger.error(f"JSON解析失败: {json_error}")
                        # 如果JSON解析失败，尝试手动解析
                        import json
                        try:
                            data = json.loads(response_text)
                            logger.info(f"手动JSON解析成功: {data}")
                        except Exception as manual_json_error:
                            logger.error(f"手动JSON解析也失败: {manual_json_error}")
                            return ResponseUtil.error("员工信息查询失败：响应格式错误")

                    # 检查返回状态
                    if data.get('state') == 200 and data.get('list') and len(data['list']) > 0:
                        employee_info = data['list'][0]
                        logger.info(f"找到员工信息: {employee_info}")

                        # 返回员工信息
                        return ResponseUtil.success(
                            data={
                                "RealName": employee_info.get('RealName'),
                                "No": employee_info.get('No')
                            },
                            msg="员工信息查询成功"
                        )
                    else:
                        # 未找到员工信息
                        logger.info(f"未找到员工信息，手机号: {mobile}")
                        return ResponseUtil.success(
                            data=None,
                            msg="未找到员工信息"
                        )
                else:
                    logger.error(f"第三方API请求失败，状态码: {response.status}")
                    return ResponseUtil.error("员工信息查询失败")

    except Exception as e:
        logger.error(f"检查员工信息异常: {str(e)}")
        return ResponseUtil.error(f"员工信息查询失败: {str(e)}")

