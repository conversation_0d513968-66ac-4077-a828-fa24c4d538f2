/**
 * 员工管理模块接口
 */
import { get, post } from '../utlis/require.js';

/**
 * 获取产品列表（员工入驻专用）- 使用公开接口
 * @param {string} invitationCode - 邀请码，必须提供
 * @returns {Promise} - 返回产品列表
 */
export const getProductList = (invitationCode) => {
  if (!invitationCode) {
    return Promise.reject(new Error('邀请码是必需参数'));
  }

  return get('/api/v1/public/product/products', { invitation_code: invitationCode }).catch(error => {
    console.error('Get Product List Error:', error);
    throw error;
  });
};

/**
 * 员工入驻
 * @param {Object} data - 员工入驻数据
 * @param {string} data.real_name - 真实姓名
 * @param {string} data.mobile - 手机号码
 * @param {string} data.id_number - 身份证号码
 * @param {string} data.sex - 性别：1-男，2-女
 * @param {string} data.age - 年龄
 * @param {string} data.avatar - 头像URL
 * @param {string} data.address - 地址
 * @param {string} data.birthday - 生日
 * @param {string} data.native_place - 籍贯
 * @param {string} data.nation - 民族
 * @param {string} data.marriage_status - 婚姻状况：0-未婚，1-已婚
 * @param {string} data.education_background - 教育背景：0-小学，1-初中，2-高中，3-大专，4-本科，5-硕士，6-博士
 * @param {string} data.height - 身高(cm)
 * @param {string} data.weight - 体重(kg)
 * @param {string} data.family_address - 家庭住址
 * @param {string} data.medical_history - 病史
 * @param {string} data.bank_name - 银行名称
 * @param {string} data.bank_card_holder - 银行卡持有人
 * @param {string} data.bank_card_number - 银行卡号
 * @param {string} data.bank_card_photo - 银行卡照片URL
 * @param {string} data.emergency_contact_name - 紧急联系人姓名
 * @param {string} data.emergency_contact_relation - 紧急联系人关系：0-其他，1-父母，2-配偶，3-子女，4-兄弟姐妹
 * @param {string} data.emergency_contact_phone - 紧急联系人电话
 * @param {string} data.emergency_contact_address - 紧急联系人地址
 * @param {string} data.work_setting - 工作设置
 * @param {string} data.travel_tool - 交通工具
 * @param {number} data.lng - 经度
 * @param {number} data.lat - 纬度
 * @param {string} data.province_id - 省份ID
 * @param {string} data.province_name - 省份名称
 * @param {string} data.area_id - 区域ID
 * @param {string} data.area_name - 区域名称
 * @param {string} data.address_desc - 详细地址描述
 * @param {Array<number>} data.product_ids - 选择的产品ID列表
 * @returns {Promise} - 返回员工入驻结果
 */
export const registerStaff = (data) => {
  console.log('员工入驻API请求数据:', JSON.stringify(data, null, 2));

  return post('/api/v1/product/staff/register', data, {
    contentType: 'application/json',
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('员工入驻API响应成功:', result);
    // 统一返回格式，确保有code字段
    return {
      code: 200,
      data: result,
      msg: '员工入驻成功'
    };
  }).catch(error => {
    console.error('员工入驻API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取员工列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.page_size - 每页数量
 * @param {string} params.status - 员工状态筛选：1-正常，0-冻结
 * @param {string} params.keyword - 搜索关键词（姓名、手机号）
 * @param {string} params.work_type - 工作类型筛选：1-到家，2-三嫂
 * @returns {Promise} - 返回员工列表和统计数据
 */
export const getStaffList = (params = {}) => {
  console.log('查询员工列表API请求参数:', JSON.stringify(params, null, 2));

  // 设置默认参数
  const requestData = {
    page: params.page || 1,
    page_size: Math.min(params.page_size || 10, 100), // 限制最大为100
    status: params.status || null,
    keyword: params.keyword || null,
    work_type: params.work_type || null
  };

  return post('/api/v1/product/staff/list', requestData, {
    contentType: 'application/json',
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询员工列表API响应成功:', result);
    // 直接返回后端响应，不再包装
    return result;
  }).catch(error => {
    console.error('查询员工列表API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取内部员工列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.page_size - 每页数量
 * @param {string} params.status - 员工状态筛选：1-正常，2-冻结，3-离职
 * @param {string} params.name - 搜索关键词（姓名）
 * @param {string} params.mobile - 搜索关键词（手机号）
 * @param {string} params.keyword - 搜索关键词（同时搜索姓名和手机号）
 * @returns {Promise} - 返回内部员工列表
 */
export const getInternalUserList = (params = {}) => {
  console.log('查询内部员工列表API请求参数:', JSON.stringify(params, null, 2));

  // 设置默认参数
  const requestParams = {
    page: params.page || 1,
    page_size: Math.min(params.page_size || 20, 50), // 限制最大为50，减少数据量
    status: params.status || '1' // 默认只查询正常状态的员工
  };

  // 传递公司ID和门店ID等筛选条件
  if (params.company_id) requestParams.company_id = params.company_id;
  if (params.store_id) requestParams.store_id = params.store_id;
  if (params.role_id) requestParams.role_id = params.role_id;

  // 处理搜索参数，避免重复传递
  if (params.keyword) {
    // 智能判断搜索类型：如果是纯数字，认为是手机号搜索，否则认为是姓名搜索
    const isPhoneNumber = /^\d+$/.test(params.keyword);
    if (isPhoneNumber) {
      requestParams.mobile = params.keyword;
    } else {
      requestParams.user_name = params.keyword;
    }
  } else {
    // 如果没有keyword，分别传递user_name和mobile
    if (params.name) requestParams.user_name = params.name;
    if (params.mobile) requestParams.mobile = params.mobile;
  }

  // 直接使用get方法传递参数，让uni.request自动处理查询参数
  return get('/api/v1/internal-user/list', requestParams, {
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询内部员工列表API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('查询内部员工列表API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取单个员工详情
 * @param {string} id - 员工ID
 * @returns {Promise} - 返回员工详情数据
 */
export const getInternalUserDetail = (id) => {
  console.log('查询员工详情API请求参数:', { id });

  if (!id) {
    return Promise.reject(new Error('员工ID不能为空'));
  }

  return get(`/api/v1/internal-user/detail/${id}`, {}, {
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询员工详情API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('查询员工详情API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 更新内部员工信息
 * @param {Object} data - 更新数据
 * @param {string} data.id - 员工ID
 * @param {string} data.name - 姓名
 * @param {string} data.mobile - 手机号
 * @param {string} data.role_id - 角色ID
 * @param {number} data.status - 状态：1-正常，2-冻结，3-离职
 * @returns {Promise} - 返回更新结果
 */
export const updateInternalUser = (data) => {
  console.log('更新内部员工信息API请求数据:', JSON.stringify(data, null, 2));

  if (!data.id) {
    return Promise.reject(new Error('员工ID不能为空'));
  }

  // 使用PUT方法，因为后端API定义为PUT
  return post('/api/v1/internal-user/update', data, {
    method: 'PUT',
    contentType: 'application/json',
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('更新内部员工信息API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('更新内部员工信息API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取可用的角色列表（排除超级管理员，只返回状态正常的角色）
 * @returns {Promise} - 返回角色列表
 */
export const getAvailableRoles = () => {
  console.log('查询可用角色列表API请求');

  return get('/api/v1/internal-user/roles', {}, {
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询可用角色列表API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('查询可用角色列表API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取内部角色列表（getAvailableRoles的别名，保持向后兼容）
 * @returns {Promise} - 返回角色列表
 */
export const getInternalRoleList = () => {
  console.log('查询内部角色列表API请求（调用getAvailableRoles）');
  return getAvailableRoles();
};

/**
 * 获取所有员工列表（包括服务人员和内部员工）
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 搜索关键词（姓名、手机号）
 * @returns {Promise} - 返回合并后的员工列表
 */
export const getAllStaffList = async (params = {}) => {
  console.log('查询所有员工列表API请求参数:', JSON.stringify(params, null, 2));

  try {
    // 使用新的销售归属人员接口
    const result = await getSalesAttributionStaffList(params);
    return result;
  } catch (error) {
    console.error('查询所有员工列表失败:', error);
    throw error;
  }
};

/**
 * 获取阿姨管理员工列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.page_size - 每页数量
 * @param {string} params.status - 员工状态筛选
 * @param {string} params.keyword - 搜索关键词（姓名、手机号）
 * @returns {Promise} - 返回阿姨管理员工列表
 */
export const getAuntManageList = (params = {}) => {
  console.log('查询三嫂员工列表API请求参数:', JSON.stringify(params, null, 2));

  // 构建请求参数，适配阿姨管理接口格式
  const requestData = {
    page: params.page || 1,
    limit: params.page_size || 10, // 阿姨接口使用limit而不是page_size
    search: params.keyword || '', // 阿姨接口使用search而不是keyword
    status: params.status || '', // 状态筛选
    sort_field: params.sort_field || '',
    sort_type: params.sort_order || '',
    type: '', // 阿姨类型
    user_uuid: '0', // 用户UUID
    age: '', // 年龄筛选
    store_uuid: '', // 门店UUID
    minage: '', // 最小年龄
    maxage: '', // 最大年龄
    hometown: '0', // 籍贯
    hometown_city: '', // 籍贯城市
    province_id: '', // 省份ID
    city_id: '', // 城市ID
    area_id: '', // 区域ID
    can_live_home: '', // 是否可住家
    aunt_skill: '', // 阿姨技能
    chinese_zodiac: '', // 生肖
    zodiac: '', // 星座
    sex: '0', // 性别
    nation: '', // 民族
    education: '', // 学历
    marry: '', // 婚姻状况
    level: '0', // 等级
    salary: '', // 期望薪资
    salary1: '', // 薪资范围1
    salary2: '', // 薪资范围2
    createTime: '', // 创建时间
    create_time: '', // 创建时间
    religion: '', // 宗教信仰
    importVisible: 'false', // 导入可见性
    insurance_status: '', // 保险状态
    search_type: '', // 搜索类型
    update_time: '', // 更新时间
    updateTime: '', // 更新时间
    aunt_source: '' // 阿姨来源
  };

  // 调用阿姨管理列表接口
  return post('/api/v1/aunt/auntmanage/list', requestData, {
    contentType: 'application/json',
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询三嫂员工列表API响应成功:', result);

    // 转换数据格式以适配现有页面
    if (result && result.list) {
      const convertedResult = {
        list: result.list.map(aunt => ({
          id: aunt.id,
          uuid: aunt.uuid || aunt.id,
          real_name: aunt.name || aunt.real_name || '未知',
          name: aunt.name || aunt.real_name || '未知',
          mobile: aunt.mobile || aunt.phone || '',
          avatar: aunt.avatar || aunt.head_img || '',
          status: aunt.status || '1',
          age: aunt.age || '未知',
          address: aunt.hometown || aunt.address || '未填写地址',
          service_cnt: aunt.service_count || aunt.order_count || 0,
          rating: aunt.rating || aunt.score || '暂无',
          skills: aunt.skill ? (Array.isArray(aunt.skill) ? aunt.skill : aunt.skill.split(',')) : [],
          create_time: aunt.create_time || aunt.created_at || '',
          // 保留原始数据以备后用
          _original: aunt
        })),
        total: result.total || 0,
        page: result.page || params.page || 1,
        total_pages: Math.ceil((result.total || 0) / (params.page_size || 10)),
        statistics: result.statistics || null
      };
      return convertedResult;
    }

    return result;
  }).catch(error => {
    console.error('查询三嫂员工列表API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取三嫂员工列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.page_size - 每页数量
 * @param {string} params.status - 员工状态筛选
 * @param {string} params.keyword - 搜索关键词（姓名、手机号）
 * @returns {Promise} - 返回三嫂员工列表
 */
export const getSansaoAuntList = (params = {}) => {
  console.log('查询三嫂员工列表API请求参数:', JSON.stringify(params, null, 2));

  // 构建请求参数，适配三嫂接口格式
  const requestData = {
    page: params.page || 1,
    limit: params.page_size || 10, // 三嫂接口使用limit而不是page_size
    search: params.keyword || '', // 三嫂接口使用search而不是keyword
    status: params.status || '', // 状态筛选
    sort_field: params.sort_field || '',
    sort_type: params.sort_order || '',
    type: '', // 阿姨类型
    user_uuid: '0', // 用户UUID
    age: '', // 年龄筛选
    store_uuid: '', // 门店UUID
    minage: '', // 最小年龄
    maxage: '', // 最大年龄
    hometown: '0', // 籍贯
    hometown_city: '', // 籍贯城市
    province_id: '', // 省份ID
    city_id: '', // 城市ID
    area_id: '', // 区域ID
    can_live_home: '', // 是否可住家
    aunt_skill: '', // 阿姨技能
    chinese_zodiac: '', // 生肖
    zodiac: '', // 星座
    sex: '0', // 性别
    nation: '', // 民族
    education: '', // 学历
    marital_status: '', // 婚姻状况
    children_num: '', // 子女数量
    height: '', // 身高
    weight: '', // 体重
    work_experience: '', // 工作经验
    salary_expectation: '', // 期望薪资
    work_time: '', // 工作时间
    work_area: '', // 工作区域
    special_skill: '', // 特殊技能
    health_status: '', // 健康状况
    id_card: '', // 身份证号
    phone: '', // 手机号
    emergency_contact: '', // 紧急联系人
    emergency_phone: '', // 紧急联系电话
    bank_card: '', // 银行卡号
    bank_name: '', // 银行名称
    account_name: '' // 开户名
  };

  // 调用三嫂阿姨管理列表接口
  return post('/prod-api/api/v1/aunts/auntmanage/list', requestData, {
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询三嫂员工列表API响应成功:', result);

    // 转换数据格式以适配现有页面
    if (result && result.data && result.data.list) {
      const convertedResult = {
        list: result.data.list.map(aunt => ({
          id: aunt.id,
          uuid: aunt.uuid || aunt.id,
          real_name: aunt.name || aunt.real_name || '未知',
          name: aunt.name || aunt.real_name || '未知',
          mobile: aunt.phone || aunt.mobile || '',
          avatar: aunt.avatar || aunt.head_img || '',
          status: aunt.status || '1',
          age: aunt.age || '未知',
          address: aunt.address || aunt.work_area || '未填写地址',
          service_cnt: aunt.service_count || aunt.order_count || 0,
          rating: aunt.rating || aunt.score || '暂无',
          skills: aunt.skills ? (Array.isArray(aunt.skills) ? aunt.skills : aunt.skills.split(',')) : [],
          create_time: aunt.create_time || aunt.created_at || '',
          // 保留原始数据以备后用
          _original: aunt
        })),
        total: result.data.total || 0,
        page: result.data.page || params.page || 1,
        total_pages: Math.ceil((result.data.total || 0) / (params.page_size || 10)),
        statistics: result.data.statistics || null
      };
      return convertedResult;
    }

    return result;
  }).catch(error => {
    console.error('查询三嫂员工列表API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取销售归属人员列表（新接口，避免参数问题）
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 搜索关键词（姓名、手机号）
 * @returns {Promise} - 返回销售归属人员列表
 */
export const getSalesAttributionStaffList = (params = {}) => {
  console.log('查询销售归属人员列表API请求参数:', JSON.stringify(params, null, 2));

  // 设置默认参数
  const requestParams = {
    keyword: params.keyword || null
  };

  return get('/api/v1/sales/attribution-staff-list', requestParams, {
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('查询销售归属人员列表API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('查询销售归属人员列表API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

/**
 * 获取员工详情
 * @param {string} staffId - 员工ID
 * @returns {Promise} - 返回员工详情
 */
export const getStaffDetail = (staffId) => {
  return get('/api/v1/staff/getStaffDetail', { staff_id: staffId }).catch(error => {
    console.error('Get Staff Detail Error:', error);
    throw error;
  });
};

/**
 * 获取技能列表
 * @returns {Promise} - 返回技能列表
 */
export const getSkillsList = () => {
  return get('/api/v1/staff/skills').catch(error => {
    console.error('Get Skills List Error:', error);
    throw error;
  });
};

/**
 * 获取员工绑定产品
 * @param {string} staffId - 员工ID
 * @returns {Promise} - 返回员工绑定的产品列表
 */
export const getStaffProducts = (staffId) => {
  return get(`/api/v1/staff/products/${staffId}`).catch(error => {
    console.error('Get Staff Products Error:', error);
    throw error;
  });
};

/**
 * 更新员工产品绑定
 * @param {string} staffId - 员工ID
 * @param {Array<number>} productIds - 产品ID列表
 * @returns {Promise} - 返回更新结果
 */
export const updateStaffProducts = (staffId, productIds) => {
  return post(`/api/v1/staff/products/${staffId}`, productIds, {
    contentType: 'application/json',
    showErr: false
  }).then(result => {
    console.log('更新员工产品绑定API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('更新员工产品绑定API请求失败:', error);
    throw error;
  });
};

/**
 * 获取门店可用产品
 * @returns {Promise} - 返回门店可用产品列表
 */
export const getAvailableProducts = () => {
  return get('/api/v1/staff/available-products').catch(error => {
    console.error('Get Available Products Error:', error);
    throw error;
  });
};

/**
 * 根据员工当前公司获取产品列表（员工端专用接口）
 * @param {string} companyId - 公司ID（可选，如果不传则获取员工默认公司的产品）
 * @returns {Promise} - 返回产品列表
 */
export const getStaffCompanyProducts = (companyId = null) => {
  const params = companyId ? { company_id: companyId } : {};
  return get('/api/v1/staff-login/company-products', params).catch(error => {
    console.error('Get Staff Company Products Error:', error);
    throw error;
  });
};

/**
 * 更新员工基本信息
 * @param {string} staffId - 员工ID
 * @param {Object} staffData - 员工更新数据
 * @param {string} staffData.mobile - 手机号
 * @param {string} staffData.current_address - 现住址
 * @returns {Promise} - 返回更新结果
 */
export const updateStaffInfo = (staffId, staffData) => {
  console.log('更新员工信息API请求数据:', JSON.stringify({ staff_id: staffId, staff_data: staffData }, null, 2));

  // 使用标准JSON格式
  const requestData = {
    staff_id: staffId,
    staff_data: staffData
  };

  return post('/api/v1/order/updateStaff', requestData, {
    contentType: 'application/json',
    showErr: false
  }).then(result => {
    console.log('更新员工信息API响应成功:', result);
    return result;
  }).catch(error => {
    console.error('更新员工信息API请求失败:', error);
    console.error('错误状态码:', error.statusCode);
    console.error('错误详情:', error.data);
    throw error;
  });
};

/**
 * 保存员工简历（三嫂）
 * @param {Object} saveData - 保存数据
 * @param {Object} saveData.auntData - aunt表数据
 * @param {Object} saveData.auntDetailData - aunt_detail表数据
 * @returns {Promise} - 返回保存结果
 */
export const saveStaffResume = (saveData) => {
  console.log('保存员工简历API请求数据:', JSON.stringify(saveData, null, 2));

  return post('/api/v1/aunt/save', saveData, {
    contentType: 'application/json',
    showErr: false // 关闭默认错误提示，由页面自己处理
  }).then(result => {
    console.log('保存员工简历API响应成功:', result);
    return {
      code: 200,
      data: result,
      msg: '简历保存成功'
    };
  }).catch(error => {
    console.error('保存员工简历API请求失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    throw error;
  });
};

// 导出所有接口函数
export default {
  getProductList,
  registerStaff,
  getStaffList,
  getInternalUserList,
  getInternalUserDetail,
  updateInternalUser,
  getAvailableRoles,
  getInternalRoleList,
  getAllStaffList,
  getAuntManageList,
  getSansaoAuntList,
  getSalesAttributionStaffList,
  getStaffDetail,
  getSkillsList,
  getStaffProducts,
  updateStaffProducts,
  getAvailableProducts,
  updateStaffInfo
};
