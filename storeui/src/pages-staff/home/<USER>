<template>
  <view class="modern-staff-my-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="dashboard-header">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 头部导航 -->
        <view class="top-nav">
          <view class="header-left"></view>
          <view class="title">我的</view>
          <view class="header-right"></view>
        </view>

        <!-- 用户信息卡片 -->
        <view class="user-info-card">
          <view class="avatar-container">
            <image
              class="avatar"
              :src="staffAvatar"
              mode="aspectFill"
              @error="onAvatarError"
            ></image>
            <view class="online-status"></view>
          </view>
          <view class="user-details">
            <view class="user-name">{{ staffName }}</view>
            <view class="company-info">
              <view class="company-label">当前公司</view>
              <view class="company-name">{{ currentCompanyName }}</view>
            </view>
          </view>
          <view
            class="company-tag"
            @click="navigateTo('/pages-staff/home/<USER>')"
            v-if="showCompanySwitchButton"
          >
            <text>切换公司</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="dashboard-content">
      <!-- 快捷功能区 -->
      <view class="quick-functions">
        <view class="function-item" @click="navigateTo('/pages-staff/home/<USER>')">
          <view class="function-icon">
            <u-icon name="calendar" color="#fdd118" size="24"></u-icon>
          </view>
          <view class="function-info">
            <text class="function-title">代客预约</text>
            <text class="function-desc">预约服务</text>
          </view>
          <view class="function-arrow">
            <u-icon name="arrow-right" color="#666" size="16"></u-icon>
          </view>
        </view>
        <view class="function-item" @click="navigateTo('/pages-staff/home/<USER>')">
          <view class="function-icon">
            <u-icon name="file-text" color="#09be89" size="24"></u-icon>
          </view>
          <view class="function-info">
            <text class="function-title">销售项目</text>
            <text class="function-desc">查看可销售项目</text>
          </view>
          <view class="function-arrow">
            <u-icon name="arrow-right" color="#666" size="16"></u-icon>
          </view>
        </view>
        <view class="function-item" @click="shareStaffInvitation">
          <view class="function-icon">
            <u-icon name="plus-circle" color="#6366f1" size="24"></u-icon>
          </view>
          <view class="function-info">
            <text class="function-title">邀请入驻</text>
            <text class="function-desc">邀请员工</text>
          </view>
          <view class="function-arrow">
            <u-icon name="arrow-right" color="#666" size="16"></u-icon>
          </view>
        </view>
        <view class="function-item" @click="contactStore">
          <view class="function-icon">
            <u-icon name="phone" color="#ff801b" size="24"></u-icon>
          </view>
          <view class="function-info">
            <text class="function-title">联系门店</text>
            <text class="function-desc">沟通协调</text>
          </view>
          <view class="function-arrow">
            <u-icon name="arrow-right" color="#666" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 开户功能卡片 -->
      <view class="modern-stats-card account-card">
        <view class="card-header">
          <view class="header-left">
            <view class="card-icon account-icon">
              <u-icon name="rmb" color="#fff" size="20"></u-icon>
            </view>
            <text>账户开户</text>
          </view>
          <u-icon name="arrow-right" color="#999" size="20"></u-icon>
        </view>
        <view class="card-content account-content">
          <!-- 未开户状态 -->
          <view class="account-status" v-if="accountStatus.need_apply">
            <view class="status-info">
              <text class="status-text">{{ accountStatus.message || '请先完成开户申请' }}</text>
              <text class="status-desc">开户后可完单直接分账，无需手工操作</text>
            </view>
            <!-- 只有在没有申请记录时才显示开户按钮 -->
            <view class="account-actions" v-if="!accountStatus.has_application">
              <button class="apply-btn" @click="showAccountModal">立即开户</button>
            </view>
          </view>

          <!-- 已开户状态 -->
          <view class="account-status" v-else>
            <view class="status-info">
              <text class="status-text">{{ accountStatus.status_name || '已开户' }}</text>
              <text class="status-desc">账户功能正常，可正常使用</text>
            </view>
            <view class="account-actions">
              <button class="balance-btn" @click="showBalanceFeature">查看余额</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 微信公众号绑定区域 -->
      <view class="modern-stats-card wechat-bind-card">
        <view class="bind-header">
          <view class="bind-title">绑定微信公众号</view>
          <view class="bind-desc">绑定后可接收订单通知、服务提醒等消息</view>
        </view>

        <view class="bind-content">
          <!-- 未绑定状态 -->
          <view class="qrcode-container" v-if="!wechatBindStatus.isBound">
            <view class="qrcode-wrapper" v-if="wechatQrCode.qrCodeUrl">
              <image :src="wechatQrCode.qrCodeUrl" class="qrcode-image" mode="aspectFit" show-menu-by-longpress="true"></image>
              <view class="qrcode-tips">长按识别二维码关注公众号</view>
            </view>
            <view class="qrcode-loading" v-else>
              <u-icon name="reload" color="#fdd118" size="80" :class="{ 'rotating': wechatQrCode.loading }"></u-icon>
              <view class="loading-text">{{ wechatQrCode.loading ? '正在生成二维码...' : '二维码生成中，请稍候' }}</view>
            </view>
          </view>

          <!-- 绑定成功状态 -->
          <view class="bind-success-container" v-if="wechatBindStatus.isBound">
            <!-- 状态卡片 -->
            <view class="status-card">
              <view class="status-header">
                <view class="status-icon">
                  <u-icon name="checkmark-circle-fill" color="#52c41a" size="48"></u-icon>
                </view>
                <view class="status-info">
                  <text class="status-title">微信公众号已绑定</text>
                  <text class="status-desc">可接收订单通知、服务提醒等消息</text>
                </view>
              </view>
            </view>

            <!-- 操作按钮区域 -->
            <view class="action-buttons">
              <view class="button-row">
                <view class="button-item primary">
                  <u-button
                    type="primary"
                    size="normal"
                    @click="testWechatPush"
                    text="测试推送"
                    :loading="testPushLoading"
                  ></u-button>
                </view>
                <view class="button-item secondary">
                  <u-button
                    type="error"
                    size="normal"
                    @click="showUnbindConfirm"
                    text="解绑公众号"
                  ></u-button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 销售提成 -->
      <view class="modern-stats-card">
        <view class="card-header">
          <view class="header-left">
            <view class="card-icon sales-icon">
              <u-icon name="rmb" color="#fff" size="20"></u-icon>
            </view>
            <text>销售提成</text>
          </view>
          <u-icon name="arrow-right" color="#999" size="20"></u-icon>
        </view>
        <view class="card-content">
          <view class="stat-item">
            <text class="label">本月销售(元)</text>
            <text class="value">{{ commissionStats.sales_commission.current_month_amount || 0 }}</text>
          </view>
          <view class="stat-item">
            <text class="label">本月销售提成(元)</text>
            <text class="value">{{ commissionStats.sales_commission.current_month_commission || 0 }}</text>
          </view>
        </view>
      </view>

      <!-- 服务单提成 -->
      <view class="modern-stats-card">
        <view class="card-header">
          <view class="header-left">
            <view class="card-icon service-icon">
              <u-icon name="list" color="#fff" size="20"></u-icon>
            </view>
            <text>服务单提成</text>
          </view>
          <u-icon name="arrow-right" color="#999" size="20"></u-icon>
        </view>
        <view class="card-content">
          <view class="stat-item">
            <text class="label">本月服务单(单)</text>
            <text class="value">{{ commissionStats.service_commission.current_month_count || 0 }}</text>
          </view>
          <view class="stat-item">
            <text class="label">本月服务提成(元)</text>
            <text class="value">{{ commissionStats.service_commission.current_month_commission || 0 }}</text>
          </view>
        </view>
      </view>

      <!-- 出勤 -->
      <view class="modern-stats-card">
        <view class="card-header">
          <view class="header-left">
            <view class="card-icon attendance-icon">
              <u-icon name="clock" color="#fff" size="20"></u-icon>
            </view>
            <text>出勤</text>
          </view>
          <view class="header-actions">
            <view class="leave-btn" @click="requestLeave">
              <text>请假</text>
            </view>
            <u-icon name="arrow-right" color="#999" size="20"></u-icon>
          </view>
        </view>
        <view class="card-content attendance">
          <view class="stat-item">
            <text class="label">工作(天)</text>
            <text class="value">30</text>
          </view>
          <view class="stat-item">
            <text class="label">病假(天)</text>
            <text class="value">0</text>
          </view>
          <view class="stat-item">
            <text class="label">加班(天)</text>
            <text class="value">0</text>
          </view>
          <view class="stat-item">
            <text class="label">事假(天)</text>
            <text class="value">0</text>
          </view>
        </view>
      </view>

      <!-- 设置按钮 -->
      <view class="modern-settings-btn" @click="navigateTo('/pages-staff/home/<USER>')">
        <view class="settings-icon">
          <u-icon name="setting" color="#666" size="24"></u-icon>
        </view>
        <view class="settings-info">
          <text class="settings-title">设置</text>
          <text class="settings-desc">个人设置与偏好</text>
        </view>
        <view class="settings-arrow">
          <u-icon name="arrow-right" color="#666" size="16"></u-icon>
        </view>
      </view>
    </view>

    <!-- 开户弹窗 -->
    <u-modal
      :show="accountModalShow"
      title="选择开户类型"
      :showCancelButton="true"
      @cancel="accountModalShow = false"
      :showConfirmButton="false"
    >
      <view class="account-modal-content">
        <view class="account-type-list">
          <view class="account-type-item" @click="selectAccountType('micro')">
            <view class="type-icon">
              <u-icon name="account" color="#fff" size="24"></u-icon>
            </view>
            <view class="type-info">
              <view class="type-title">个人开户</view>
              <view class="type-desc">适用于个人用户，快速开户</view>
            </view>
            <view class="type-arrow">
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </u-modal>

    <!-- 底部TabBar -->
    <view class="staff-tabbar">
      <view class="tab-item" @click="switchTab('/pages-staff/home/<USER>')">
        <image src="/static/img/tab/map.png" mode="aspectFit"></image>
        <text>服务</text>
      </view>
      <view class="tab-item active">
        <image src="/static/img/tab/my_a.png" mode="aspectFit"></image>
        <text>我的</text>
      </view>
    </view>


  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getStaffCommissionStatistics } from '@/api/staff-order.js';
import { checkStaffWechatBindStatus, generateStaffWechatQrCode } from '@/api/staff-auth.js';
import { checkStaffAccountStatus } from '@/api/staff-account-application.js';

export default {
  data() {
    return {
      defaultAvatar: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png',
      commissionStats: {
        sales_commission: {
          current_month_amount: 0,
          current_month_commission: 0
        },
        service_commission: {
          current_month_count: 0,
          current_month_commission: 0
        }
      },
      wechatBindStatus: { // 微信绑定状态
        isBound: false,
        bindTime: null,
        wechatNickname: null,
        wechatAvatar: null
      },
      wechatQrCode: { // 微信二维码相关
        loading: false,
        qrCodeUrl: '',
        expireSeconds: 1800, // 30分钟
        expireMinutes: 30,
        countdown: 0,
        timer: null,
        checkTimer: null,
        checkingBind: false
      },
      testPushLoading: false, // 测试推送加载状态

      // 开户状态相关
      accountStatus: {
        has_application: false,
        need_apply: true,
        status: null,
        status_name: '未开户',
        message: '请先完成开户申请',
        application_uuid: null,
        yeepay_customer_code: null
      },
      accountModalShow: false // 开户类型选择弹窗
    };
  },
  computed: {
    ...mapState(['storeInfo', 'staffInfo', 'currentRole', 'StatusBar']),
    ...mapGetters(['hasDualRole', 'getCurrentSelectedCompany']),

    // 员工头像
    staffAvatar() {
      if (this.staffInfo && this.staffInfo.avatar) {
        return this.staffInfo.avatar;
      }
      return this.defaultAvatar;
    },

    // 员工姓名
    staffName() {
      if (this.staffInfo && this.staffInfo.real_name) {
        return this.staffInfo.real_name;
      }
      return '员工';
    },



    // 当前公司名称（优先使用选中的公司）
    currentCompanyName() {
      // 优先使用当前选中的公司
      const selectedCompany = this.getCurrentSelectedCompany;
      if (selectedCompany && selectedCompany.store_name) {
        return selectedCompany.store_name;
      }

      // 如果没有选中公司，使用员工信息中的公司
      if (this.staffInfo && this.staffInfo.store_name) {
        return this.staffInfo.store_name;
      }
      return '暂无公司信息';
    },

    // 是否显示公司切换按钮
    showCompanySwitchButton() {
      return this.staffInfo &&
             this.staffInfo.companies &&
             this.staffInfo.companies.length > 1;
    }
  },

  mounted() {
    this.checkStaffInfo();
    this.loadCommissionStatistics();
    this.checkWechatBindStatus();
    this.checkAccountStatus();
  },

  beforeDestroy() {
    // 清理定时器
    this.clearTimers();
  },

  onShow() {
    // 页面显示时刷新提成统计数据和开户状态
    this.loadCommissionStatistics();
    this.checkAccountStatus();
  },

  methods: {
    navigateTo(url) {
      uni.navigateTo({
        url,
      });
    },

    // 联系门店按钮点击事件
    async contactStore() {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '获取联系方式...'
        });

        // 调用API获取门店联系方式
        const { getStoreContact } = require('../../api/staff-auth.js');
        const result = await getStoreContact();

        uni.hideLoading();

        if (result && result.phone) {
          // 直接拨号，不显示确认弹窗
          uni.makePhoneCall({
            phoneNumber: result.phone,
            success: () => {
              console.log('拨号成功');
            },
            fail: (err) => {
              console.error('拨号失败:', err);
              uni.showToast({
                title: '拨号失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '门店暂未设置联系电话',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('获取门店联系方式失败:', error);
        uni.showToast({
          title: error.message || '获取联系方式失败',
          icon: 'none'
        });
      }
    },
    switchTab(url) {
      uni.reLaunch({
        url,
      });
    },
    requestLeave() {
      uni.showToast({
        title: '请假功能开发中',
        icon: 'none',
      });
    },

    // 头像加载失败处理
    onAvatarError() {
      console.log('头像加载失败，使用默认头像');
      // 这里可以设置一个标志来使用默认头像
    },

    // 检查员工信息
    checkStaffInfo() {
      console.log('当前员工信息:', this.staffInfo);

      // 如果Vuex中没有员工信息，尝试从本地存储获取
      if (!this.staffInfo) {
        const localStaffInfo = uni.getStorageSync('staffInfo');
        console.log('从本地存储获取的员工信息:', localStaffInfo);

        if (localStaffInfo) {
          // 更新Vuex状态
          this.$store.commit('Updates', { staffInfo: localStaffInfo });
        } else {
          // 如果都没有，提示用户重新登录
          console.warn('未找到员工信息，可能需要重新登录');
          uni.showModal({
            title: '提示',
            content: '未找到员工信息，是否重新登录？',
            success: (res) => {
              if (res.confirm) {
                console.log('用户确认重新登录，跳转到统一登录页');
                uni.reLaunch({
                  url: '/pages/login/login'
                });
              }
            }
          });
        }
      }
    },

    // 加载提成统计数据
    async loadCommissionStatistics() {
      try {
        console.log('开始加载员工提成统计数据');

        // 获取当前选中的门店UUID（如果有的话）
        const selectedCompany = this.getCurrentSelectedCompany;
        const storeUuid = selectedCompany ? selectedCompany.store_uuid : null;

        const response = await getStaffCommissionStatistics(storeUuid);
        console.log('提成统计API响应:', response);


          this.commissionStats = response;
          console.log('提成统计数据更新成功:', this.commissionStats);

      } catch (error) {
        console.error('加载提成统计数据失败:', error);
        // 不显示错误提示，保持静默失败，避免影响用户体验
      }
    },

    // 检查微信绑定状态
    async checkWechatBindStatus() {
      try {
        console.log('开始检查员工微信绑定状态...');
        const result = await checkStaffWechatBindStatus();
        console.log('员工微信绑定状态检查成功:', result);

        this.wechatBindStatus = {
          isBound: result.isBound || false,
          bindTime: result.bindTime || null,
          wechatNickname: result.wechatNickname || null,
          wechatAvatar: result.wechatAvatar || null
        };

        // 如果未绑定，自动生成二维码并开始轮询
        if (!this.wechatBindStatus.isBound) {
          await this.generateQrCode(true); // 静默生成
          this.startBindStatusPolling();
        } else {
          // 如果已绑定，停止轮询
          this.stopBindStatusPolling();
        }

      } catch (error) {
        console.error('检查员工微信绑定状态失败:', error);
        // 检查失败时设置为未绑定状态，生成二维码
        this.wechatBindStatus = {
          isBound: false,
          bindTime: null,
          wechatNickname: null,
          wechatAvatar: null
        };
        await this.generateQrCode(true);
        this.startBindStatusPolling();
      }
    },

    // 处理微信绑定（现在主要用于刷新二维码）
    handleWechatBind() {
      if (!this.wechatBindStatus.isBound) {
        // 未绑定时，刷新二维码
        this.refreshQrCode();
      }
    },

    // 显示微信绑定信息
    showWechatBindInfo() {
      const bindTime = this.formatBindTime(this.wechatBindStatus.bindTime);

      uni.showModal({
        title: '微信绑定信息',
        content: `昵称：${this.wechatBindStatus.wechatNickname || '未知'}\n绑定时间：${bindTime}`,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 显示解绑确认
    showUnbindConfirm() {
      uni.showModal({
        title: '解绑确认',
        content: '解绑后将无法接收微信公众号推送通知，确定要解绑吗？',
        success: (res) => {
          if (res.confirm) {
            // 跳转到解绑页面（需要验证码）
            uni.showToast({
              title: '请联系管理员解绑',
              icon: 'none'
            });
          }
        }
      });
    },

    // 格式化绑定时间
    formatBindTime(bindTime) {
      if (!bindTime) return '未知';
      return new Date(bindTime).toLocaleString();
    },

    // 生成二维码
    async generateQrCode(silent = false) {
      if (!this.staffInfo || !this.staffInfo.uuid) {
        if (!silent) {
          uni.showToast({
            title: '员工信息获取失败',
            icon: 'none'
          });
        }
        return;
      }

      this.wechatQrCode.loading = true;

      try {
        const result = await generateStaffWechatQrCode({
          staff_id: this.staffInfo.uuid,
          expire_seconds: this.wechatQrCode.expireSeconds
        });

        console.log('生成员工微信二维码成功:', result);

        this.wechatQrCode.qrCodeUrl = result.qrCodeUrl;
        this.wechatQrCode.expireSeconds = result.expire_seconds || this.wechatQrCode.expireSeconds;
        this.wechatQrCode.expireMinutes = Math.floor(this.wechatQrCode.expireSeconds / 60);

        // 开始倒计时
        this.startCountdown();

      } catch (error) {
        console.error('生成员工微信二维码失败:', error);
        if (!silent) {
          uni.showToast({
            title: error?.msg || error?.message || '生成二维码失败',
            icon: 'none'
          });
        }
      } finally {
        this.wechatQrCode.loading = false;
      }
    },

    // 开始倒计时
    startCountdown() {
      this.wechatQrCode.countdown = this.wechatQrCode.expireSeconds;
      this.wechatQrCode.timer = setInterval(() => {
        this.wechatQrCode.countdown--;
        if (this.wechatQrCode.countdown <= 0) {
          clearInterval(this.wechatQrCode.timer);
          this.wechatQrCode.timer = null;
          uni.showToast({
            title: '二维码已过期，请刷新',
            icon: 'none'
          });
        }
      }, 1000);
    },

    // 开始绑定状态轮询
    startBindStatusPolling() {
      // 每5秒检查一次绑定状态
      this.wechatQrCode.checkTimer = setInterval(() => {
        this.checkBindStatusPolling();
      }, 5000);
    },

    // 停止绑定状态轮询
    stopBindStatusPolling() {
      if (this.wechatQrCode.checkTimer) {
        clearInterval(this.wechatQrCode.checkTimer);
        this.wechatQrCode.checkTimer = null;
      }
    },

    // 轮询检查绑定状态
    async checkBindStatusPolling() {
      try {
        this.wechatQrCode.checkingBind = true;
        const result = await checkStaffWechatBindStatus();

        console.log('轮询检查员工微信绑定状态:', result);

        if (result.isBound) {
          // 检测到绑定状态，更新页面状态
          const wasUnbound = !this.wechatBindStatus.isBound;

          this.wechatBindStatus = {
            isBound: result.isBound || false,
            bindTime: result.bindTime || null,
            wechatNickname: result.wechatNickname || null,
            wechatAvatar: result.wechatAvatar || null
          };

          this.stopBindStatusPolling();
          this.clearTimers();

          // 只有从未绑定变为绑定时才显示成功提示
          if (wasUnbound) {
            uni.showToast({
              title: '🎉 绑定成功！',
              icon: 'success',
              duration: 3000
            });
          }

          // 强制更新页面
          this.$forceUpdate();
        }
      } catch (error) {
        console.error('轮询检查绑定状态失败:', error);
      } finally {
        this.wechatQrCode.checkingBind = false;
      }
    },

    // 格式化倒计时显示
    formatCountdown(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 刷新二维码
    refreshQrCode() {
      this.clearTimers();
      this.generateQrCode();
    },

    // 清除定时器
    clearTimers() {
      if (this.wechatQrCode.timer) {
        clearInterval(this.wechatQrCode.timer);
        this.wechatQrCode.timer = null;
      }
      if (this.wechatQrCode.checkTimer) {
        clearInterval(this.wechatQrCode.checkTimer);
        this.wechatQrCode.checkTimer = null;
      }
    },

    // 测试微信推送
    async testWechatPush() {
      if (!this.wechatBindStatus.isBound) {
        uni.showToast({
          title: '请先绑定微信公众号',
          icon: 'none'
        });
        return;
      }

      this.testPushLoading = true;

      try {
        console.log('开始测试微信推送...');

        // 调用测试推送API
        const { testStaffWechatPush } = require('../../api/staff-auth.js');
        const result = await testStaffWechatPush();

        console.log('测试推送API响应:', result);

        // 如果API调用成功返回数据（没有抛出异常），就表示推送发送成功
        if (result !== null && result !== undefined) {
          uni.showToast({
            title: '测试推送发送成功',
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: '推送发送失败',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('测试微信推送失败:', error);
        uni.showToast({
          title: error.message || '推送发送失败',
          icon: 'none'
        });
      } finally {
        this.testPushLoading = false;
      }
    },

    // 切换身份功能已移至role-switcher-pill组件中

    // 检查开户状态
    async checkAccountStatus() {
      try {
        // 获取当前员工手机号
        if (!this.staffInfo || !this.staffInfo.mobile) {
          console.warn('未获取到员工手机号，跳过开户状态检查');
          return;
        }

        console.log('检查开户状态，手机号:', this.staffInfo.mobile);

        // 调用员工端检查开户状态API
        const result = await checkStaffAccountStatus();

        console.log('开户状态检查结果:', result);

        // 更新开户状态信息
        this.accountStatus = {
          has_application: result.has_application || false,
          need_apply: result.need_apply || true,
          status: result.status,
          status_name: result.status_name || '未开户',
          message: result.message || '请先完成开户申请',
          application_uuid: result.application_uuid,
          yeepay_customer_code: result.yeepay_customer_code
        };

      } catch (error) {
        console.error('检查开户状态失败:', error);
        // 检查失败时设置默认状态
        this.accountStatus = {
          has_application: false,
          need_apply: true,
          status: null,
          status_name: '未开户',
          message: '请先完成开户申请',
          application_uuid: null,
          yeepay_customer_code: null
        };
      }
    },

    // 显示开户类型选择弹窗
    showAccountModal() {
      this.accountModalShow = true;
    },

    // 选择开户类型
    selectAccountType(type) {
      this.accountModalShow = false;

      if (type === 'micro') {
        // 个人开户申请
        uni.navigateTo({
          url: '/pages-other/account-application/index',
          fail: (err) => {
            console.error('跳转个人开户页面失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    },

    // 显示余额功能（暂时提示功能开发中）
    showBalanceFeature() {
      uni.showToast({
        title: '余额功能开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // 员工分享邀请入驻
    shareStaffInvitation() {
      try {
        // 获取当前选中公司对应的员工ID
        const currentStaffId = this.getCurrentCompanyStaffId();

        if (!currentStaffId) {
          uni.showToast({
            title: '获取当前公司员工信息失败',
            icon: 'error'
          });
          return;
        }

        // 触发微信小程序原生分享
        uni.showShareMenu({
          withShareTicket: true,
          success: () => {
            uni.showToast({
              title: '请点击右上角分享',
              icon: 'none'
            });
          },
          fail: (err) => {
            console.error('分享菜单显示失败:', err);
            // 分享失败时提供复制链接的备选方案
            uni.setClipboardData({
              data: `${this.staffName}邀请您加入${this.currentCompanyName}，邀请码：${currentStaffId}`,
              success: () => {
                uni.showToast({
                  title: '邀请信息已复制到剪贴板',
                  icon: 'success'
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('分享邀请失败:', error);
        uni.showToast({
          title: '分享失败',
          icon: 'error'
        });
      }
    },

    // 获取当前公司对应的员工ID
    getCurrentCompanyStaffId() {
      try {
        // 获取当前选中的公司信息
        const currentCompany = this.getCurrentSelectedCompany;

        if (!currentCompany || !currentCompany.company_id || !currentCompany.store_id) {
          console.warn('当前选中公司信息不完整:', currentCompany);
          // 如果没有选中公司，使用默认的员工ID
          return this.staffInfo?.id || null;
        }

        // 从员工信息的companies数组中查找当前公司对应的员工记录
        if (this.staffInfo && this.staffInfo.companies && Array.isArray(this.staffInfo.companies)) {
          const currentCompanyStaff = this.staffInfo.companies.find(company =>
            company.company_id === currentCompany.company_id &&
            company.store_id === currentCompany.store_id
          );

          if (currentCompanyStaff && currentCompanyStaff.id) {
            console.log('找到当前公司对应的员工ID:', currentCompanyStaff.id);
            return currentCompanyStaff.id;
          }
        }

        // 如果在companies数组中没有找到，使用默认的员工ID
        console.warn('未找到当前公司对应的员工记录，使用默认员工ID');
        return this.staffInfo?.id || null;
      } catch (error) {
        console.error('获取当前公司员工ID失败:', error);
        return this.staffInfo?.id || null;
      }
    },
  },

  // 微信小程序分享功能
  onShareAppMessage() {
    try {
      // 实时获取当前员工邀请信息，不依赖临时状态
      const currentStaffId = this.getCurrentCompanyStaffId();

      if (currentStaffId && this.staffName && this.currentCompanyName) {
        // 有员工信息，返回邀请分享
        const invitationUrl = `/pages-home/attendantManage-add?invitation_code=${currentStaffId}`;

        console.log('员工端分享邀请入驻:', {
          staffId: currentStaffId,
          staffName: this.staffName,
          companyName: this.currentCompanyName,
          url: invitationUrl
        });

        return {
          title: `${this.staffName}邀请您加入${this.currentCompanyName}`,
          path: invitationUrl,
          imageUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png'
        };
      }
    } catch (error) {
      console.error('获取员工邀请信息失败:', error);
    }

    // 默认分享内容（当获取员工信息失败时）
    console.log('使用默认分享内容');
    return {
      title: '家政服务好帮手，进来逛逛吧~',
      path: '/pages/login/login?tg=' + uni.getStorageSync('tg') + '&shareScene=' + uni.getStorageSync('scene'),
      imageUrl: 'https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png'
    };
  },
};
</script>

<style lang="scss" scoped>
// 现代化员工端个人中心样式
.modern-staff-my-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 头部区域
.dashboard-header {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;
  // 添加状态栏安全区域
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }

  .top-nav {
    display: flex;
    align-items: center;
    padding: 0;
    height: 100rpx;
    margin-bottom: 30rpx;

    .header-left {
      width: 120rpx;
      padding-left: 0;
    }

    .header-right {
      width: 120rpx;
      padding-right: 0;
      display: flex;
      justify-content: flex-end;
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
      text-align: center;
      flex: 1;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }
  }

  .user-info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    position: relative;

    .avatar-container {
      position: relative;
      margin-right: 20rpx;

      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        border: 3rpx solid rgba(253, 209, 24, 0.3);
      }

      .online-status {
        position: absolute;
        bottom: 2rpx;
        right: 2rpx;
        width: 16rpx;
        height: 16rpx;
        background: #09be89;
        border-radius: 8rpx;
        border: 2rpx solid #fff;
      }
    }

    .user-details {
      flex: 1;

      .user-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 12rpx;
      }

      .company-info {
        display: flex;
        flex-direction: column;
        gap: 4rpx;

        .company-label {
          font-size: 20rpx;
          color: #999;
          font-weight: 500;
          letter-spacing: 0.5rpx;
        }

        .company-name {
          font-size: 26rpx;
          color: #666;
          font-weight: 600;
          line-height: 1.4;
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }
    }

    .company-tag {
      background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
      border-radius: 20rpx;
      padding: 8rpx 16rpx;
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);

      text {
        color: #fff;
        font-size: 22rpx;
        font-weight: 500;
      }
    }
  }
}

// 内容区域
.dashboard-content {
  padding: 0 20rpx;
}

// 快捷功能区
.quick-functions {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;

  .function-item {
    display: flex;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background: rgba(253, 209, 24, 0.05);
    }

    .function-icon {
      width: 56rpx;
      height: 56rpx;
      border-radius: 28rpx;
      background: rgba(253, 209, 24, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
    }

    .function-info {
      flex: 1;

      .function-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 4rpx;
        display: block;
      }

      .function-desc {
        font-size: 24rpx;
        color: #999;
        display: block;
      }
    }

    .function-arrow {
      margin-left: 16rpx;
    }
  }
}

// 现代化统计卡片
.modern-stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .header-left {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .card-icon {
        width: 40rpx;
        height: 40rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.sales-icon {
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        }

        &.service-icon {
          background: linear-gradient(135deg, #09be89 0%, #00d4aa 100%);
        }

        &.attendance-icon {
          background: linear-gradient(135deg, #2196f3 0%, #21cbf3 100%);
        }

        &.account-icon {
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        }
      }

      text {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .leave-btn {
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        border-radius: 20rpx;
        padding: 8rpx 16rpx;
        box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);

        text {
          color: #fff;
          font-size: 22rpx;
          font-weight: 500;
        }
      }
    }
  }

  .card-content {
    display: flex;
    padding: 24rpx 0;

    .stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 12rpx 0;

      .label {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
        font-weight: 500;
      }

      .value {
        font-size: 36rpx;
        color: #333;
        font-weight: 600;
      }
    }

    &.attendance {
      flex-wrap: wrap;
      padding: 16rpx 0;

      .stat-item {
        width: 50%;
        flex: none;
        margin-bottom: 16rpx;
      }
    }
  }
}

// 现代化设置按钮
.modern-settings-btn {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  padding: 24rpx;
  transition: all 0.3s ease;

  &:active {
    background: rgba(253, 209, 24, 0.05);
    transform: scale(0.98);
  }

  .settings-icon {
    width: 56rpx;
    height: 56rpx;
    border-radius: 28rpx;
    background: rgba(102, 102, 102, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
  }

  .settings-info {
    flex: 1;

    .settings-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 4rpx;
      display: block;
    }

    .settings-desc {
      font-size: 24rpx;
      color: #999;
      display: block;
    }
  }

  .settings-arrow {
    margin-left: 16rpx;
  }
}

// 底部TabBar样式
.staff-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;

    image {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 6rpx;
      transition: all 0.3s ease;
    }

    text {
      font-size: 24rpx;
      color: #999;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    &.active {
      text {
        color: #fdd118;
        font-weight: 600;
      }

      image {
        transform: scale(1.1);
      }
    }

    &:active {
      background: rgba(253, 209, 24, 0.05);
    }
  }

  .card-icon.wechat-icon {
    background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
  }
}

/* 微信绑定卡片样式（参考门店端设计） */
.wechat-bind-card {
  .bind-header {
    padding: 24rpx 30rpx 20rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

    .bind-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }

    .bind-desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.4;
    }
  }

  .bind-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
  }

  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qrcode-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qrcode-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
    border: 1rpx solid #eee;
  }

  .qrcode-tips {
    font-size: 24rpx;
    color: #999;
    margin-top: 16rpx;
    text-align: center;
  }

  /* 操作按钮区域样式 */
  .action-buttons {
    margin-top: 20rpx;

    .button-row {
      display: flex;
      gap: 16rpx;
      justify-content: center;

      .button-item {
        flex: 1;
        max-width: 200rpx;

        &.primary {
          // 测试推送按钮样式
        }

        &.secondary {
          // 解绑按钮样式
        }
      }
    }
  }

  .qrcode-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #ddd;
    border-radius: 12rpx;
    background: #fafafa;
  }

  .loading-text {
    font-size: 24rpx;
    color: #999;
    margin-top: 12rpx;
    text-align: center;
  }

  /* 绑定成功容器样式 */
  .bind-success-container {
    width: 100%;
    padding: 24rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 20rpx;
    border: 1rpx solid #e9ecef;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }

  /* 状态卡片 */
  .status-card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  }

  .status-header {
    display: flex;
    align-items: center;
  }

  .status-icon {
    margin-right: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    background: rgba(82, 196, 26, 0.1);
    border-radius: 50%;
  }

  .status-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .status-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 8rpx;
  }

  .status-desc {
    font-size: 26rpx;
    color: #666;
    line-height: 1.4;
  }

  /* 操作按钮区域 */
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  .button-row {
    width: 100%;
  }

  .button-item {
    width: 100%;
  }
}

/* 旋转动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 开户卡片样式 */
.account-card {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    z-index: 1;
  }

  .account-content {
    padding: 24rpx 30rpx;
  }

  .account-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .status-info {
    flex: 1;
    margin-right: 20rpx;

    .status-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
      margin-bottom: 8rpx;
      display: block;
    }

    .status-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.4;
      display: block;
    }
  }

  .account-actions {
    .apply-btn, .balance-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      border: none;
      border-radius: 20rpx;
      padding: 12rpx 24rpx;
      font-size: 24rpx;
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
      }
    }
  }
}

/* 开户弹窗样式 */
.account-modal-content {
  padding: 20rpx 0;

  .account-type-list {
    .account-type-item {
      display: flex;
      align-items: center;
      padding: 24rpx 20rpx;
      border-radius: 12rpx;
      margin-bottom: 16rpx;
      background: #f8f9fa;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background: rgba(253, 209, 24, 0.05);
        transform: scale(0.98);
      }

      .type-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 30rpx;
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        font-size: 32rpx;
        box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);
      }

      .type-info {
        flex: 1;

        .type-title {
          font-size: 28rpx;
          color: #333;
          font-weight: 600;
          margin-bottom: 8rpx;
          display: block;
        }

        .type-desc {
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
          display: block;
        }
      }

      .type-arrow {
        margin-left: 16rpx;
      }
    }
  }
}
</style>

