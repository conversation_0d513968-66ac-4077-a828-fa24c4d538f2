<template>
  <view class="modern-login-container">
    <!-- 顶部背景区域 -->
    <view class="header-background-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 左上角入驻申请按钮 -->
        <view class="top-left-join-btn" @click="goToJoinStore">
          <u-icon name="plus-circle" color="#fff" size="18"></u-icon>
          <text class="join-btn-text">入驻申请</text>
        </view>



        <!-- Logo区域 -->
        <view class="logo-section">
          <view class="logo-container">
            <image class="logo-image" :src="logoUrl" mode="aspectFit" />
          </view>
          <view class="brand-text">
            <text class="brand-title">金刚到家</text>
            <text class="brand-subtitle">智能家政管理系统 || 成就家政人</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="content-wrapper">

      <!-- 身份切换器 -->
      <view class="role-switcher">
        <view class="role-tabs">
          <view
            class="role-tab"
            :class="{ active: selectedRole === 'store' }"
            @click="switchRole('store')"
          >
            <view class="role-icon">
              <u-icon name="home" :color="selectedRole === 'store' ? '#fff' : '#fdd118'" size="20"></u-icon>
            </view>
            <text class="role-text">门店管理端</text>
          </view>
          <view
            class="role-tab"
            :class="{ active: selectedRole === 'staff' }"
            @click="switchRole('staff')"
          >
            <view class="role-icon">
              <u-icon name="account" :color="selectedRole === 'staff' ? '#fff' : '#fdd118'" size="20"></u-icon>
            </view>
            <text class="role-text">员工接单端</text>
          </view>
        </view>
      </view>

      <!-- 门店端登录卡片 -->
      <view class="login-card store-login-card" v-show="selectedRole === 'store'">
        <view class="card-glow"></view>

        <!-- 门店端手机号快速验证按钮 - 放在登录区域顶部 -->
        <view class="wechat-login-section" style="margin-top: 20rpx; margin-bottom: 30rpx;">
          <view class="social-btn" @click="handleStoreWechatLogin">
            <u-icon
              name="phone"
              size="24"
              color="#fff"
              class="social-icon"
            ></u-icon>
            <text class="social-text">手机号快速验证</text>
          </view>
        </view>

        <!-- 卡片标题 -->
        <view class="card-title">
          <view class="title-icon">
            <u-icon name="home" color="#fdd118" size="24"></u-icon>
          </view>
          <text class="title-text">门店管理端登录</text>
        </view>

        <!-- Tab切换 - 已隐藏登录方式选择项 -->
        <!-- <view class="modern-tabs">
          <view class="tab-slider" :style="{ transform: 'translateX(' + (currentTab === 'phone' ? '0%' : '100%') + ')' }"></view>
          <view
            class="tab-item"
            :class="{ active: currentTab === 'phone' }"
            @click="switchTab('phone')"
          >
            <text>账号登录</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: currentTab === 'code' }"
            @click="switchTab('code')"
          >
            <text>验证码登录</text>
          </view>
        </view> -->

        <!-- 表单区域 -->
        <view class="form-section">
          <!-- 手机号输入 -->
          <view class="input-group">
            <view class="input-wrapper" :class="{ focused: phoneFocused }">
              <view class="input-icon">
                <u-icon name="phone" color="#fdd118" size="20"></u-icon>
              </view>
              <input
                class="modern-input"
                type="number"
                v-model="phone"
                maxlength="11"
                placeholder="请输入手机号"
                @focus="phoneFocused = true"
                @blur="phoneFocused = false"
              />
            </view>
          </view>

          <!-- 密码/验证码输入 -->
          <view class="input-group" v-if="currentTab === 'phone'">
            <view class="input-wrapper" :class="{ focused: passwordFocused }">
              <view class="input-icon">
                <u-icon name="lock" color="#fdd118" size="20"></u-icon>
              </view>
              <input
                class="modern-input"
                :type="showPsw ? 'text' : 'password'"
                :key="'password-input-' + showPsw"
                v-model="password"
                placeholder="请输入密码"
                @focus="passwordFocused = true"
                @blur="passwordFocused = false"
                :password="!showPsw"
              />
              <view class="input-suffix">
                <view class="password-toggle" @click.stop="togglePasswordVisibility">
                  <u-icon :name="showPsw ? 'eye-fill' : 'eye-off'" color="#999" size="20"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 验证码输入 - 已隐藏 -->
          <!-- <view class="input-group" v-else>
            <view class="input-wrapper" :class="{ focused: codeFocused }">
              <view class="input-icon">
                <u-icon name="checkmark-circle" color="#fdd118" size="20"></u-icon>
              </view>
              <input
                class="modern-input"
                v-model="code"
                placeholder="请输入验证码"
                @focus="codeFocused = true"
                @blur="codeFocused = false"
              />
              <view class="input-suffix">
                <u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
                <view class="code-btn" @click="getCode" :class="{ disabled: !canGetCode }" style="pointer-events: auto;">
                  <text>{{ tips }}</text>
                </view>
              </view>
            </view>
          </view> -->

          <!-- 记住密码选项 -->
          <view class="remember-section" v-if="currentTab === 'phone'">
            <view class="remember-wrapper" @click="toggleRememberPassword">
              <view class="custom-checkbox" :class="{ checked: rememberPassword }">
                <u-icon name="checkmark" color="#fdd118" size="18" v-if="rememberPassword"></u-icon>
              </view>
              <text class="remember-text">记住密码</text>
            </view>
          </view>

          <!-- 协议勾选 -->
          <view class="agreement-section">
            <view class="agreement-wrapper" @click="toggleAgreement">
              <view class="custom-checkbox" :class="{ checked: isAgreed }">
                <u-icon name="checkmark" color="#fdd118" size="18" v-if="isAgreed"></u-icon>
              </view>
              <view class="agreement-content">
                <text class="agreement-text">我已阅读并同意</text>
                <text class="agreement-link" @click.stop="openWebview('https://h5.jingangai.cn/ServiceAgreement.html')">《服务协议》</text>
                <text class="agreement-text">和</text>
                <text class="agreement-link" @click.stop="openWebview('https://h5.jingangai.cn/PrivacyAgreement.html')">《隐私政策》</text>
              </view>
            </view>
          </view>

          <!-- 登录按钮 -->
          <view class="login-btn-wrapper">
            <view
              class="modern-login-btn"
              :class="{ loading: isLoading }"
              @click="handleLogin"
            >
              <view class="btn-bg"></view>
              <view class="btn-content">
                <u-loading-icon v-if="isLoading" color="#fff" size="20"></u-loading-icon>
                <text v-else>{{ isLoading ? '登录中...' : '立即登录' }}</text>
              </view>
            </view>
          </view>

          <!-- 底部链接 -->
          <view class="bottom-actions">
            <text class="action-link" @click="goUrl('./forgetPsw')">忘记密码?</text>
            <text class="action-link register-link" @click="goUrl('./register')">
              家政公司<text class="highlight">入驻申请</text>
            </text>
          </view>

          <!-- 门店端手机号快速验证 - 已隐藏，使用顶部按钮 -->
          <!-- <view class="wechat-login-section">
            <view class="divider-line">
              <text class="divider-text">其他登录方式</text>
            </view>
            <view class="social-btn" @click="handleStoreWechatLogin">
              <u-icon
                name="phone"
                size="24"
                color="#fff"
                class="social-icon"
              ></u-icon>
              <text class="social-text">手机号快速验证</text>
            </view>
          </view> -->
        </view>
      </view>

      <!-- 员工端登录卡片 -->
      <view class="login-card staff-login-card" v-show="selectedRole === 'staff'">
        <view class="card-glow"></view>

        <!-- 员工端手机号快速验证按钮 - 放在登录区域顶部 -->
        <view class="wechat-login-section" style="margin-top: 20rpx; margin-bottom: 30rpx;">
          <button
            class="social-btn"
            open-type="getPhoneNumber"
            @getphonenumber="handleStaffPhoneAuth"
            :disabled="staffPhoneAuthLoading"
          >
            <u-loading-icon
              v-if="staffPhoneAuthLoading"
              color="#fff"
              size="24"
              class="social-icon"
            ></u-loading-icon>
            <u-icon
              v-else
              name="phone"
              size="24"
              color="#fff"
              class="social-icon"
            ></u-icon>
            <text class="social-text">{{ staffPhoneAuthLoading ? '登录中...' : '手机号快速验证' }}</text>
          </button>
        </view>

        <!-- 卡片标题 -->
        <view class="card-title">
          <view class="title-icon">
            <u-icon name="account" color="#fdd118" size="24"></u-icon>
          </view>
          <text class="title-text">员工接单端登录</text>
        </view>

        <!-- Tab切换 - 已隐藏登录方式选择项 -->
        <!-- <view class="modern-tabs">
          <view class="tab-slider" :style="{ transform: 'translateX(' + (staffCurrentTab === 'phone' ? '0%' : '100%') + ')' }"></view>
          <view
            class="tab-item"
            :class="{ active: staffCurrentTab === 'phone' }"
            @click="switchStaffTab('phone')"
          >
            <text>账号登录</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: staffCurrentTab === 'code' }"
            @click="switchStaffTab('code')"
          >
            <text>验证码登录</text>
          </view>
        </view> -->

        <!-- 表单区域 -->
        <view class="form-section">
          <!-- 手机号输入 -->
          <view class="input-group">
            <view class="input-wrapper" :class="{ focused: staffPhoneFocused }">
              <view class="input-icon">
                <u-icon name="phone" color="#fdd118" size="20"></u-icon>
              </view>
              <input
                class="modern-input"
                type="number"
                v-model="staffPhone"
                maxlength="11"
                placeholder="请输入手机号"
                @focus="staffPhoneFocused = true"
                @blur="staffPhoneFocused = false"
              />
            </view>
          </view>

          <!-- 密码输入 -->
          <view class="input-group" v-if="staffCurrentTab === 'phone'">
            <view class="input-wrapper" :class="{ focused: staffPasswordFocused }">
              <view class="input-icon">
                <u-icon name="lock" color="#fdd118" size="20"></u-icon>
              </view>
              <input
                class="modern-input"
                :type="staffShowPsw ? 'text' : 'password'"
                :key="'staff-password-input-' + staffShowPsw"
                v-model="staffPassword"
                placeholder="请输入密码"
                @focus="staffPasswordFocused = true"
                @blur="staffPasswordFocused = false"
                :password="!staffShowPsw"
              />
              <view class="input-suffix">
                <view class="password-toggle" @click.stop="toggleStaffPasswordVisibility">
                  <u-icon :name="staffShowPsw ? 'eye-fill' : 'eye-off'" color="#999" size="20"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 验证码输入 - 已隐藏 -->
          <!-- <view class="input-group" v-else>
            <view class="input-wrapper" :class="{ focused: staffCodeFocused }">
              <view class="input-icon">
                <u-icon name="checkmark-circle" color="#fdd118" size="20"></u-icon>
              </view>
              <input
                class="modern-input"
                v-model="staffCode"
                placeholder="请输入验证码"
                @focus="staffCodeFocused = true"
                @blur="staffCodeFocused = false"
              />
              <view class="input-suffix">
                <u-code ref="staffUCode" @change="staffCodeChange" seconds="60" changeText="X秒重新获取"></u-code>
                <view class="code-btn" @click="getStaffCode" :class="{ disabled: !staffCanGetCode }" style="pointer-events: auto;">
                  <text>{{ staffTips }}</text>
                </view>
              </view>
            </view>
          </view> -->

          <!-- 记住密码选项 -->
          <view class="remember-section" v-if="staffCurrentTab === 'phone'">
            <view class="remember-wrapper" @click="toggleStaffRememberPassword">
              <view class="custom-checkbox" :class="{ checked: staffRememberPassword }">
                <u-icon name="checkmark" color="#fdd118" size="18" v-if="staffRememberPassword"></u-icon>
              </view>
              <text class="remember-text">记住密码</text>
            </view>
          </view>

          <!-- 协议勾选 -->
          <view class="agreement-section">
            <view class="agreement-wrapper" @click="toggleStaffAgreement">
              <view class="custom-checkbox" :class="{ checked: staffIsAgreed }">
                <u-icon name="checkmark" color="#fdd118" size="18" v-if="staffIsAgreed"></u-icon>
              </view>
              <view class="agreement-content">
                <text class="agreement-text">我已阅读并同意</text>
                <text class="agreement-link" @click.stop="openWebview('https://h5.jingangai.cn/ServiceAgreement.html')">《服务协议》</text>
                <text class="agreement-text">和</text>
                <text class="agreement-link" @click.stop="openWebview('https://h5.jingangai.cn/PrivacyAgreement.html')">《隐私政策》</text>
              </view>
            </view>
          </view>

          <!-- 登录按钮 -->
          <view class="login-btn-wrapper">
            <view
              class="modern-login-btn"
              :class="{ loading: staffIsLoading }"
              @click="handleStaffLogin"
            >
              <view class="btn-bg"></view>
              <view class="btn-content">
                <u-loading-icon v-if="staffIsLoading" color="#fff" size="20"></u-loading-icon>
                <text v-else>{{ staffIsLoading ? '登录中...' : '立即登录' }}</text>
              </view>
            </view>
          </view>

          <!-- 底部链接 -->
          <view class="bottom-actions">
            <text class="action-link" @click="goStaffForgetPassword">忘记密码?</text>
            <!-- 员工入职申请标签已隐藏 -->
            <!-- <text class="action-link register-link">
              员工<text class="highlight">入职申请</text>
            </text> -->
          </view>

          <!-- 员工端手机号快速验证 - 已隐藏，使用顶部按钮 -->
          <!-- <view class="wechat-login-section">
            <view class="divider-line">
              <text class="divider-text">其他登录方式</text>
            </view>
            <button
              class="social-btn"
              open-type="getPhoneNumber"
              @getphonenumber="handleStaffPhoneAuth"
              :disabled="staffPhoneAuthLoading"
            >
              <u-loading-icon
                v-if="staffPhoneAuthLoading"
                color="#fff"
                size="24"
                class="social-icon"
              ></u-loading-icon>
              <u-icon
                v-else
                name="phone"
                size="24"
                color="#fff"
                class="social-icon"
              ></u-icon>
              <text class="social-text">{{ staffPhoneAuthLoading ? '登录中...' : '手机号快速验证' }}</text>
            </button>
          </view> -->
        </view>
      </view>
    </view>

    <!-- 可拖动在线客服按钮 -->
    <view
      class="draggable-service-btn"
      :style="{
        left: serviceButtonPosition.left + 'px',
        top: serviceButtonPosition.top + 'px'
      }"
      @touchstart.stop.prevent="onServiceTouchStart"
      @touchmove.stop.prevent="onServiceTouchMove"
      @touchend.stop.prevent="onServiceTouchEnd"
    >
      <view class="service-icon">
        <u-icon name="server-man" color="#fff" size="20"></u-icon>
      </view>
      <view class="service-text">在线客服</view>
    </view>


  </view>
</template>

<script>
import { userLogin, userInfo } from '@/api/user.js';
// import { getCode } from '@/api/common.js'; // 验证码相关已隐藏
import { staffPasswordLogin, /* staffSmsLogin, sendSmsCode as staffSendSmsCode, */ staffPhoneQuickAuth } from '@/api/staff-auth.js'; // 验证码相关已隐藏
import { post } from '@/utlis/require.js';
import store from '@/vuex/index.js';

export default {
  data() {
    return {
      logoUrl: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png',
      selectedRole: 'store', // 当前选择的身份
      currentTab: 'phone', // 固定为phone模式，隐藏验证码登录
      phone: '',
      password: '',
      // tips: '获取验证码', // 验证码相关已隐藏
      // code: '', // 验证码相关已隐藏
      showPsw: false,
      isAgreed: false,
      isLoading: false,
      phoneFocused: false,
      passwordFocused: false,
      // codeFocused: false, // 验证码相关已隐藏
      // canGetCode: true, // 验证码相关已隐藏
      rememberPassword: false,

      // 员工端登录相关数据
      staffCurrentTab: 'phone', // 固定为phone模式，隐藏验证码登录
      staffPhone: '',
      staffPassword: '',
      // staffTips: '获取验证码', // 验证码相关已隐藏
      // staffCode: '', // 验证码相关已隐藏
      staffShowPsw: false,
      staffIsAgreed: false,
      staffIsLoading: false,
      staffPhoneFocused: false,
      staffPasswordFocused: false,
      // staffCodeFocused: false, // 验证码相关已隐藏
      // staffCanGetCode: true, // 验证码相关已隐藏
      staffRememberPassword: false,
      staffPhoneAuthLoading: false, // 员工端手机号授权加载状态

      // 可拖动客服按钮相关数据
      serviceButtonPosition: {
        left: 0, // 将在mounted中动态计算右下角位置
        top: 0
      },
      // 屏幕信息缓存，避免拖动时重复获取
      screenInfo: {
        windowWidth: 375,
        windowHeight: 667
      },
      isDragging: false,
      startPosition: {
        x: 0,
        y: 0
      },
      touchStartTime: 0,

    };
  },
  computed: {
    token() {
      return !this.getAppData('token');
    },
    sys() {
      return this.getAppData('sys');
    },
    isX() {
      return this.getAppData('isX');
    },
  },
  onLoad(option) {
    console.log('登录页面加载，参数:', option);

    // 检查是否有角色参数，如果有则设置对应的登录身份
    if (option && option.role === 'staff') {
      this.selectedRole = 'staff';
      console.log('检测到员工端登录参数，自动切换到员工端登录');
    }

    // 检查当前页面是否为白名单页面（如注册页面）
    const isCurrentPageWhitelisted = () => {
      try {
        const pages = getCurrentPages();
        if (pages && pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          const currentRoute = currentPage.route;

          // 白名单页面列表
          const whitelistPages = [
            'pages/login/register',
            'pages-other/joinStore/process',
            'pages/login/login',
            'pages/aunt-resume/detail',
            'pages-public/lead-share',
            'pages-public/order-share',  // 订单分享页面
            'pages-large/training-list',  // 培训列表页面（支持分享免登录访问）
            'pages-large/training-detail'  // 培训详情页面（支持分享免登录访问）
          ];

          const isWhitelisted = whitelistPages.some(whitelistPath => {
            return currentRoute === whitelistPath || currentRoute.includes(whitelistPath);
          });

          console.log('登录页面检查当前页面是否为白名单页面:', currentRoute, '结果:', isWhitelisted);
          return isWhitelisted;
        }
      } catch (error) {
        console.error('登录页面检查白名单页面失败:', error);
      }
      return false;
    };

    // 如果当前页面是白名单页面（如注册页面），跳过登录状态检查
    if (isCurrentPageWhitelisted()) {
      console.log('✅ 登录页面检测到白名单页面，跳过登录状态检查和自动跳转');
      // 如果没有角色参数，默认设置为门店端
      if (!option || !option.role) {
        this.selectedRole = 'store';
      }
      return;
    }

    // 检查用户是否已经登录
    const isLogin = uni.getStorageSync('isLogin');
    const token = uni.getStorageSync('token');
    const staffToken = uni.getStorageSync('staffToken');
    const currentRole = uni.getStorageSync('currentRole');

    console.log('登录状态检查:', { isLogin, hasToken: !!token, hasStaffToken: !!staffToken, currentRole });

    // 如果用户已经登录，直接跳转到对应首页
    if (isLogin && (token || staffToken)) {
      console.log('用户已登录，跳转到对应首页');
      if (currentRole === 'staff' && staffToken) {
        uni.reLaunch({
          url: '/pages-staff/home/<USER>'
        });
      } else if (token) {
        uni.reLaunch({
          url: '/pages/home/<USER>'
        });
      }
      return;
    }

    // 未登录用户，如果没有角色参数则重置为默认的门店端选择
    if (!option || !option.role) {
      this.selectedRole = 'store';
    }
    console.log('未登录用户，当前选择角色:', this.selectedRole);
  },
  onShow() {
    // 加载保存的账号密码
    this.loadSavedCredentials();

    // 显示当前登录端提示弹窗
    this.showLoginTypeModal();

    // if (process.env.NODE_ENV === 'development') {
    //   this.phone = 13600000001;
    //   this.password = '123456';
    // }
    // this.setAppData('token', '');
  },

  // 组件挂载时获取屏幕信息
  mounted() {
    this.initScreenInfo();
    this.initServiceButtonPosition();
  },

  // 微信小程序转发功能
  onShareAppMessage() {
    let shareobj = {
      title: '家政服务好帮手，进来逛逛吧~', //分享的标题
      path: '/pages/login/login?tg=' + uni.getStorageSync('tg') + '&shareScene=' + uni.getStorageSync('scene'), //好友点击分享之后跳转的页面
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png", //分享的图片
    };
    return shareobj;
  },

  methods: {
    // 初始化屏幕信息，避免拖动时重复获取
    initScreenInfo() {
      try {
        // 使用新的API替代废弃的uni.getSystemInfoSync
        let windowInfo = {};

        // 尝试使用新的API
        if (uni.getWindowInfo) {
          windowInfo = uni.getWindowInfo();
        } else {
          // 降级到旧API，但添加错误处理
          const systemInfo = uni.getSystemInfoSync();
          windowInfo = {
            windowWidth: systemInfo.windowWidth,
            windowHeight: systemInfo.windowHeight
          };
        }

        // 更新缓存的屏幕信息，确保有默认值
        this.screenInfo = {
          windowWidth: windowInfo.windowWidth || 375,
          windowHeight: windowInfo.windowHeight || 667
        };

        console.log('屏幕信息已缓存:', this.screenInfo);
      } catch (error) {
        console.error('获取屏幕信息失败，使用默认值:', error);
        // 使用默认值
        this.screenInfo = {
          windowWidth: 375,
          windowHeight: 667
        };
      }
    },

    // 初始化客服按钮位置为右下角
    initServiceButtonPosition() {
      // 设置客服按钮默认位置为右下角
      const buttonWidth = 70; // 按钮宽度
      const buttonHeight = 80; // 按钮高度
      const rightMargin = 20; // 距离右边的边距
      const bottomMargin = 150; // 距离底部的边距

      this.serviceButtonPosition = {
        left: this.screenInfo.windowWidth - buttonWidth - rightMargin,
        top: this.screenInfo.windowHeight - buttonHeight - bottomMargin
      };

      console.log('客服按钮初始位置设置为右下角:', this.serviceButtonPosition);
    },

    // switchTab方法 - 已隐藏Tab切换功能
    // switchTab(tab) {
    //   this.currentTab = tab;
    // },

    // 身份切换
    switchRole(role) {
      this.selectedRole = role;
      console.log('切换登录身份:', role === 'store' ? '门店管理端' : '员工接单端');

      // 显示切换后的登录端提示弹窗
      this.showLoginTypeModal();
    },

    // 员工端Tab切换方法 - 已隐藏Tab切换功能
    // switchStaffTab(tab) {
    //   this.staffCurrentTab = tab;
    // },

    togglePasswordVisibility() {
      console.log('=== togglePasswordVisibility方法被调用 ===');
      console.log('密码显示切换前:', this.showPsw);
      console.log('当前密码值:', this.password);

      this.showPsw = !this.showPsw;

      console.log('密码显示切换后:', this.showPsw);
      console.log('图标名称应该是:', this.showPsw ? 'eye-fill' : 'eye-off');

      // 强制更新视图
      this.$forceUpdate();

      // 延迟检查DOM更新
      this.$nextTick(() => {
        console.log('DOM更新完成，当前showPsw状态:', this.showPsw);
      });
    },

    toggleAgreement() {
      this.isAgreed = !this.isAgreed;
    },

    toggleRememberPassword() {
      this.rememberPassword = !this.rememberPassword;
    },

    // 员工端密码显示切换
    toggleStaffPasswordVisibility() {
      this.staffShowPsw = !this.staffShowPsw;
      this.$forceUpdate();
    },

    // 员工端协议切换
    toggleStaffAgreement() {
      this.staffIsAgreed = !this.staffIsAgreed;
    },

    // 员工端记住密码切换
    toggleStaffRememberPassword() {
      this.staffRememberPassword = !this.staffRememberPassword;
    },



    handleAgreementChange(e) {
      this.isAgreed = e.detail.value.length > 0;
    },

    // 加载保存的账号密码
    loadSavedCredentials() {
      try {
        // 加载管理端保存的账号密码
        const savedCredentials = uni.getStorageSync('savedCredentials');
        if (savedCredentials) {
          this.phone = savedCredentials.phone || '';
          this.password = savedCredentials.password || '';
          this.rememberPassword = true;
        }

        // 加载员工端保存的账号密码
        const staffSavedCredentials = uni.getStorageSync('staffSavedCredentials');
        if (staffSavedCredentials) {
          this.staffPhone = staffSavedCredentials.phone || '';
          this.staffPassword = staffSavedCredentials.password || '';
          this.staffRememberPassword = true;
        }
      } catch (error) {
        console.log('加载保存的账号密码失败:', error);
      }
    },

    // 保存账号密码
    saveCredentials() {
      try {
        if (this.rememberPassword && this.phone && this.password) {
          uni.setStorageSync('savedCredentials', {
            phone: this.phone,
            password: this.password
          });
        } else {
          // 如果不记住密码，清除保存的信息
          uni.removeStorageSync('savedCredentials');
        }
      } catch (error) {
        console.log('保存账号密码失败:', error);
      }
    },

    // 保存员工端账号密码
    saveStaffCredentials() {
      try {
        if (this.staffRememberPassword && this.staffPhone && this.staffPassword) {
          uni.setStorageSync('staffSavedCredentials', {
            phone: this.staffPhone,
            password: this.staffPassword
          });
          console.log('员工端账号密码已保存');
        } else {
          // 如果不记住密码，清除保存的信息
          uni.removeStorageSync('staffSavedCredentials');
          console.log('员工端账号密码已清除');
        }
      } catch (error) {
        console.log('保存员工端账号密码失败:', error);
      }
    },
    handleLogin() {
      if (this.isLoading) {
        return;
      }

      // 智能检查服务协议
      this.checkAgreementAndProceed(false, () => {
        // 固定使用账号密码登录，验证码登录已隐藏
        if (this.currentTab === 'phone') {
          this.btnLogin();
        }
        // else {
        //   this.handleCodeLogin(); // 验证码登录已隐藏
        // }
      });
    },
    // 验证码相关方法 - 已隐藏
    // codeChange(text) {
    //   this.tips = text;
    // },
    // getCode() {
    //   console.log('=== getCode方法被调用 ===');
    //   console.log('当前手机号:', this.phone);
    //   console.log('canGetCode状态:', this.canGetCode);
    //   console.log('uCode组件引用:', this.$refs.uCode);
    //   console.log('uCode.canGetCode状态:', this.$refs.uCode?.canGetCode);
    //
    //   if (!this.validatePhone()) {
    //     console.log('手机号验证失败');
    //     return;
    //   }
    //
    //   if (this.$refs.uCode && this.$refs.uCode.canGetCode) {
    //     console.log('开始发送验证码...');
    //     this.canGetCode = false;
    //     uni.showLoading({
    //       title: '正在获取验证码',
    //     });
    //
    //     // 调用实际的验证码发送接口
    //     console.log('调用getCode API，参数:', { mobile: this.phone });
    //     getCode({ mobile: this.phone })
    //       .then((response) => {
    //         console.log('验证码发送成功，响应:', response);
    //         uni.hideLoading();
    //         this.showMsg('验证码已发送');
    //         this.$refs.uCode.start();
    //         this.canGetCode = true;
    //       })
    //       .catch(err => {
    //         console.error('验证码发送失败，错误:', err);
    //         uni.hideLoading();
    //         // 如果发送失败，停止倒计时
    //         this.canGetCode = true;
    //         this.showMsg(err?.msg || err?.message || '发送失败，请重试');
    //       });
    //   } else {
    //     console.log('不能发送验证码，原因:');
    //     console.log('- uCode组件存在:', !!this.$refs.uCode);
    //     console.log('- uCode.canGetCode:', this.$refs.uCode?.canGetCode);
    //     this.showMsg('倒计时结束后再发送');
    //   }
    // },
    btnLogin() {
      try {
        if (!this.validatePhone()) throw '请输入联系电话';
        if (!this.password) throw '请输入密码';
      } catch (errMsg) {
        this.showMsg(errMsg);
        return;
      }

      // 设置加载状态
      this.isLoading = true;

      // 调用实际登录接口
      userLogin({
        mobile: this.phone,
        password: this.password
      }).then(res => {
        // 重置加载状态
        this.isLoading = false;

        // 保存账号密码（如果勾选了记住密码）
        this.saveCredentials();

        // 设置登录状态
        store.commit('Updates', { isLogin: true });
        uni.setStorageSync('isLogin', true);

        // 设置门店端角色
        store.commit('Updates', { currentRole: 'store' });
        uni.setStorageSync('currentRole', 'store');

        // 调用用户信息函数获取详细信息并缓存（与其他登录方式保持一致）
        userInfo().then(userInfoResult => {
          console.log('获取用户详细信息成功:', userInfoResult);
          // 用户信息已经在validateToken中缓存，这里只是确认

          // 登录成功后检查版本过期状态
          this.checkVersionExpiryAfterLogin();
        }).catch(error => {
          console.log('获取用户详细信息失败:', error);
          // 即使获取用户信息失败，也要检查版本过期状态
          this.checkVersionExpiryAfterLogin();
        });
      }).catch(error => {
        // 重置加载状态
        this.isLoading = false;

        // 显示错误信息
        const errorMessage = error?.message || '账号或密码错误';
        this.showMsg(errorMessage);
        console.error('登录失败:', error);
      });
    },
    // 验证码登录方法 - 已隐藏
    // handleCodeLogin() {
    //   // 验证码登录逻辑
    //   try {
    //     if (!this.validatePhone()) throw '请输入联系电话';
    //     if (!this.code) throw '请输入验证码';
    //   } catch (errMsg) {
    //     this.showMsg(errMsg);
    //     return;
    //   }
    //
    //   // 设置加载状态
    //   this.isLoading = true;
    //
    //   // 调用验证码登录接口
    //   // 注意：这里需要替换为实际的验证码登录API
    //   post('/api/v1/auth/login-by-code', {
    //     mobile: this.phone,
    //     code: this.code
    //   }).then(res => {
    //     // 重置加载状态
    //     this.isLoading = false;
    //
    //     // 存储登录信息
    //     store.commit('Updates', { token: res.user_token, isLogin: true });
    //     uni.setStorageSync('token', res.user_token);
    //     uni.setStorageSync('isLogin', true);
    //
    //     // 处理双重身份信息
    //     if (res.storeInfo) {
    //       store.commit('Updates', { storeInfo: res.storeInfo });
    //       uni.setStorageSync('storeInfo', res.storeInfo);
    //     }
    //
    //     if (res.staffInfo) {
    //       store.commit('Updates', { staffInfo: res.staffInfo });
    //       uni.setStorageSync('staffInfo', res.staffInfo);
    //     }
    //
    //     // 设置门店端角色
    //     store.commit('Updates', { currentRole: 'store' });
    //     uni.setStorageSync('currentRole', 'store');
    //
    //     // 调用用户信息函数获取详细信息并缓存（与账号密码登录保持一致）
    //     userInfo().then(userInfoResult => {
    //       console.log('获取用户详细信息成功:', userInfoResult);
    //       // 用户信息已经在validateToken中缓存，这里只是确认
    //
    //       // 登录成功后检查版本过期状态
    //       this.checkVersionExpiryAfterLogin();
    //     }).catch(error => {
    //       console.log('获取用户详细信息失败:', error);
    //       // 即使获取用户信息失败，也要检查版本过期状态
    //       this.checkVersionExpiryAfterLogin();
    //     });
    //   }).catch(error => {
    //     // 重置加载状态
    //     this.isLoading = false;
    //
    //     // 显示错误信息
    //     const errorMessage = error?.message || '验证码错误或已过期';
    //     this.showMsg(errorMessage);
    //     console.error('验证码登录失败:', error);
    //   });
    // },
    goUrl(url) {
      uni.navigateTo({
        url: url,
      });
    },

    // 跳转到入驻申请页面
    goToJoinStore() {
      uni.navigateTo({
        url: './register'
      });
    },

    // 跳转到在线客服页面
    goToCustomerService() {
      // 跳转到企业微信客服页面
      	wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfcd47295cb0436a5ad' //客服地址链接
					},
					corpId: 'ww70a37a20913e26d3', //必须和你小程序上的一致
					success(res) {
						console.log(res, 1)
					},
					fail(res) {
						console.log(res, 2)
					},
				})
    },

    // 客服按钮拖动相关方法
    onServiceTouchStart(e) {
      this.isDragging = false;
      this.startPosition = {
        x: e.touches[0].clientX,
        y: e.touches[0].clientY
      };
      this.touchStartTime = Date.now();
    },

    onServiceTouchMove(e) {
      this.isDragging = true;

      // 计算移动距离
      const deltaX = e.touches[0].clientX - this.startPosition.x;
      const deltaY = e.touches[0].clientY - this.startPosition.y;

      // 使用缓存的屏幕信息，避免重复调用废弃的API
      const maxX = this.screenInfo.windowWidth - 70; // 按钮宽度约70px
      const maxY = this.screenInfo.windowHeight - 80; // 按钮高度约80px

      let newLeft = this.serviceButtonPosition.left + deltaX;
      let newTop = this.serviceButtonPosition.top + deltaY;

      // 边界检测
      newLeft = Math.max(10, Math.min(newLeft, maxX));
      newTop = Math.max(100, Math.min(newTop, maxY)); // 顶部留出100px空间

      this.serviceButtonPosition.left = newLeft;
      this.serviceButtonPosition.top = newTop;

      // 更新起始位置
      this.startPosition.x = e.touches[0].clientX;
      this.startPosition.y = e.touches[0].clientY;
    },

    onServiceTouchEnd() {
      console.log('客服按钮触摸结束');
      const touchEndTime = Date.now();
      const touchDuration = touchEndTime - this.touchStartTime;

      // 如果是短时间点击且没有拖动，触发点击事件
      if (!this.isDragging && touchDuration < 300) {
        console.log('触发客服按钮点击');
        this.goToCustomerService();
      }

      this.isDragging = false;
    },
    validatePhone() {
      const phoneRegex = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!phoneRegex.test(this.phone)) {
        this.showMsg('请输入有效的手机号码');
        return false;
      }
      return true;
    },
    showMsg(msg) {
      uni.showToast({
        title: msg,
        icon: 'none',
      });
    },

    // 显示登录端类型提示
    showLoginTypeModal() {
      const loginType = this.selectedRole === 'store' ? '管理端登录' : '服务人员端登录';

      uni.showToast({
        title: loginType,
        icon: 'none',
        duration: 2000
      });
    },

    // 员工端验证码相关方法 - 已隐藏
    // staffCodeChange(text) {
    //   this.staffTips = text;
    // },
    //
    // getStaffCode() {
    //   if (!this.validateStaffPhone()) {
    //     return;
    //   }
    //
    //   if (this.$refs.staffUCode && this.$refs.staffUCode.canGetCode) {
    //     this.staffCanGetCode = false;
    //     uni.showLoading({
    //       title: '正在获取验证码',
    //     });
    //
    //     // 调用服务人员验证码发送接口
    //     staffSendSmsCode({ mobile: this.staffPhone })
    //       .then((response) => {
    //         uni.hideLoading();
    //         this.showMsg('验证码已发送');
    //         this.$refs.staffUCode.start();
    //         this.staffCanGetCode = true;
    //       })
    //       .catch(err => {
    //         uni.hideLoading();
    //         this.staffCanGetCode = true;
    //         this.showMsg(err?.msg || err?.message || '发送失败，请重试');
    //       });
    //   } else {
    //     this.showMsg('倒计时结束后再发送');
    //   }
    // },

    validateStaffPhone() {
      const phoneRegex = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!phoneRegex.test(this.staffPhone)) {
        this.showMsg('请输入有效的手机号码');
        return false;
      }
      return true;
    },

    // 员工端登录处理
    handleStaffLogin() {
      if (this.staffIsLoading) {
        return;
      }

      // 智能检查服务协议
      this.checkAgreementAndProceed(true, () => {
        // 固定使用账号密码登录，验证码登录已隐藏
        if (this.staffCurrentTab === 'phone') {
          this.staffBtnLogin();
        }
        // else {
        //   this.handleStaffCodeLogin(); // 验证码登录已隐藏
        // }
      });
    },

    // 员工端密码登录
    staffBtnLogin() {
      try {
        if (!this.validateStaffPhone()) throw '请输入联系电话';
        if (!this.staffPassword) throw '请输入密码';
      } catch (errMsg) {
        this.showMsg(errMsg);
        return;
      }

      // 设置加载状态
      this.staffIsLoading = true;

      // 调用员工端密码登录API
      staffPasswordLogin({
        mobile: this.staffPhone,
        password: this.staffPassword
      }).then(res => {
        // 重置加载状态
        this.staffIsLoading = false;

        // 保存员工端账号密码（如果勾选了记住密码）
        this.saveStaffCredentials();

        // 登录成功提示
        this.showMsg('员工端登录成功！');

        // 跳转到员工端首页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages-staff/home/<USER>'
          });
        }, 1500);
      }).catch(error => {
        // 重置加载状态
        this.staffIsLoading = false;

        // 显示错误信息
        const errorMessage = error?.message || '账号或密码错误';
        this.showMsg(errorMessage);
        console.error('员工端密码登录失败:', error);
      });
    },

    // 员工端验证码登录方法 - 已隐藏
    // handleStaffCodeLogin() {
    //   try {
    //     if (!this.validateStaffPhone()) throw '请输入联系电话';
    //     if (!this.staffCode) throw '请输入验证码';
    //   } catch (errMsg) {
    //     this.showMsg(errMsg);
    //     return;
    //   }
    //
    //   // 设置加载状态
    //   this.staffIsLoading = true;
    //
    //   // 调用服务人员短信登录API
    //   staffSmsLogin({
    //     mobile: this.staffPhone,
    //     sms_code: this.staffCode
    //   }).then(res => {
    //     // 重置加载状态
    //     this.staffIsLoading = false;
    //
    //     // 登录成功提示
    //     this.showMsg('员工端登录成功！');
    //
    //     // 跳转到员工端首页
    //     setTimeout(() => {
    //       uni.reLaunch({
    //         url: '/pages-staff/home/<USER>'
    //       });
    //     }, 1500);
    //   }).catch(error => {
    //     // 重置加载状态
    //     this.staffIsLoading = false;
    //
    //     // 显示错误信息
    //     const errorMessage = error?.message || '验证码错误或已过期';
    //     this.showMsg(errorMessage);
    //     console.error('员工端验证码登录失败:', error);
    //   });
    // },

    // 门店端微信登录
    handleStoreWechatLogin() {
      // 处理微信登录逻辑
      console.log('微信登录');

      // 智能检查服务协议
      this.checkAgreementAndProceed(false, () => {
        // 显示加载提示
        uni.showLoading({
          title: '微信登录中...',
          mask: true
        });

        // 调用微信登录API
        uni.login({
          provider: 'weixin',
          success: (loginRes) => {
            console.log('微信登录授权成功:', loginRes);

            // 直接使用微信登录，不获取用户信息
            this.processWechatLogin(loginRes.code);
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('微信授权失败:', err);
            this.showMsg('微信授权失败，请重试');
          }
        });
      });
    },

    // 统一的微信登录处理方法
    processWechatLogin(code) {
      // 获取设备信息，优先使用新API
      let devicePlatform = 'unknown';
      try {
        if (uni.getDeviceInfo) {
          const deviceInfo = uni.getDeviceInfo();
          devicePlatform = deviceInfo.platform || 'unknown';
        } else {
          // 降级到旧API
          const systemInfo = uni.getSystemInfoSync();
          devicePlatform = systemInfo.platform || 'unknown';
        }
      } catch (error) {
        console.error('获取设备信息失败:', error);
        devicePlatform = 'unknown';
      }

      const loginData = {
        code: code,
        login_info: JSON.stringify({
          platform: 'uniapp',
          device: devicePlatform
        })
      };

      console.log('微信登录请求数据:', loginData);

      post('/api/v1/internal-login/wechat-login', loginData, { contentType: 'application/json' })
        .then(res => {
          uni.hideLoading();
          console.log('微信登录响应:', res);

          if (res.need_bind) {
            // 需要绑定手机号，跳转到绑定页面
            uni.navigateTo({
              url: `/pages/login/wechatBind?wx_openid=${res.wx_openid}`
            });
          } else {
            // 登录成功
            this.handleLoginSuccess(res);
          }
        })
        .catch(err => {
          uni.hideLoading();
          console.error('微信登录失败:', err);
          this.showMsg(err?.msg || err?.message || '微信登录失败，请重试');
        });
    },

    // 处理登录成功
    handleLoginSuccess(res) {
      console.log('=== 微信登录成功，开始存储token ===');
      console.log('接收到的token:', res.user_token);

      // 直接存储到本地存储和Vuex
      try {
        // 存储到本地存储
        uni.setStorageSync('token', res.user_token);
        uni.setStorageSync('isLogin', true);

        // 存储到Vuex
        store.commit('Updates', { token: res.user_token, isLogin: true });

        console.log('Token已存储到本地存储和Vuex');
      } catch (error) {
        console.error('存储token失败:', error);
      }

      // 处理双重身份信息（如果有）
      if (res.storeInfo) {
        store.commit('Updates', { storeInfo: res.storeInfo });
        uni.setStorageSync('storeInfo', res.storeInfo);
      }

      if (res.staffInfo) {
        store.commit('Updates', { staffInfo: res.staffInfo });
        uni.setStorageSync('staffInfo', res.staffInfo);
      }

      // 设置门店端角色
      store.commit('Updates', { currentRole: 'store' });
      uni.setStorageSync('currentRole', 'store');

      // 调用用户信息函数获取详细信息并缓存（与账号密码登录保持一致）
      userInfo().then(userInfoResult => {
        console.log('获取用户详细信息成功:', userInfoResult);
        // 用户信息已经在validateToken中缓存，这里只是确认

        // 登录成功后检查版本过期状态
        this.checkVersionExpiryAfterLogin();
      }).catch(error => {
        console.log('获取用户详细信息失败:', error);
        // 即使获取用户信息失败，也要检查版本过期状态
        this.checkVersionExpiryAfterLogin();
      });
    },

    // 员工端手机号快速验证登录
    async handleStaffPhoneAuth(e) {
      // 处理员工端手机号快速验证登录逻辑
      console.log('员工端手机号快速验证登录', e);

      // 检查用户是否授权
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        console.log('用户拒绝微信手机号授权');
        this.showMsg('需要手机号授权才能快速登录');
        return;
      }

      // 智能检查服务协议
      this.checkAgreementAndProceed(true, async () => {
        this.staffPhoneAuthLoading = true;

        try {
          // 先获取微信登录code
          const loginRes = await this.wxLogin();

          if (!loginRes.code) {
            throw new Error('微信登录失败');
          }

          // 调用员工手机号快速验证登录接口
          const result = await staffPhoneQuickAuth({
            wx_code: loginRes.code,
            encrypted_data: e.detail.encryptedData,
            iv: e.detail.iv
          });

          console.log('员工手机号快速验证登录成功:', result);

          this.showMsg('登录成功！');

          // 跳转到员工端首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages-staff/home/<USER>'
            });
          }, 1500);

        } catch (error) {
          console.error('员工手机号快速验证登录失败:', error);

          let errorMsg = '登录失败，请重试';
          if (error?.message) {
            errorMsg = error.message;
          } else if (error?.msg) {
            errorMsg = error.msg;
          }

          this.showMsg(errorMsg);
        } finally {
          this.staffPhoneAuthLoading = false;
        }
      });
    },

    // 微信登录
    wxLogin() {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: resolve,
          fail: reject
        });
      });
    },

    // 员工端忘记密码
    goStaffForgetPassword() {
      console.log('员工端忘记密码');
      uni.navigateTo({
        url: './forgetPsw?role=staff'
      });
    },

    openWebview(url) {
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(url)}`
      });
    },

    // 智能检查服务协议并处理
    async checkAgreementAndProceed(isStaff = false, callback) {
      const isAgreed = isStaff ? this.staffIsAgreed : this.isAgreed;

      if (isAgreed) {
        // 已勾选，直接执行回调
        callback();
        return;
      }

      // 未勾选，弹出确认对话框
      uni.showModal({
        title: '服务协议确认',
        content: '是否同意《服务协议》和《隐私政策》？',
        confirmText: '同意',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户选择同意，自动勾选协议
            if (isStaff) {
              this.staffIsAgreed = true;
            } else {
              this.isAgreed = true;
            }

            // 继续执行登录流程
            callback();
          } else {
            // 用户选择取消，不执行登录
            console.log('用户取消了服务协议确认');
          }
        }
      });
    },

    // 登录成功后检查版本过期状态
    async checkVersionExpiryAfterLogin() {
      try {
        console.log('开始检查版本过期状态...');

        // 导入版本过期检查API
        const { checkVersionExpiry } = await import('@/api/company.js');

        // 调用版本过期检查接口
        const result = await checkVersionExpiry();
        console.log('版本过期检查结果:', result);

        if (result && result.all_expired) {
          // 所有版本都已过期，显示过期提示并退出登录
          console.log('检测到所有版本都已过期，准备退出登录');

          // 显示过期提示
          uni.showModal({
            title: '版本已过期',
            content: result.message || '您的软件版本已过期，请联系平台进行续费',
            showCancel: false,
            confirmText: '确定',
            success: () => {
              // 清除登录状态
              this.logoutAndClearData();
            }
          });
        } else {
          // 版本状态正常，继续跳转到主页
          console.log('版本状态正常，跳转到门店端首页');
          this.navigateToHomePage();
        }
      } catch (error) {
        console.error('检查版本过期状态失败:', error);
        // 如果检查失败，为了不影响用户体验，仍然跳转到主页
        console.log('版本检查失败，继续跳转到门店端首页');
        this.navigateToHomePage();
      }
    },

    // 清除登录数据并退出
    logoutAndClearData() {
      try {
        console.log('开始清除登录数据...');

        // 清除Vuex状态
        store.commit('clearAuth', true);

        // 清除本地存储
        uni.removeStorageSync('token');
        uni.removeStorageSync('isLogin');
        uni.removeStorageSync('user');
        uni.removeStorageSync('storeInfo');
        uni.removeStorageSync('staffInfo');
        uni.removeStorageSync('currentRole');

        console.log('登录数据已清除');

        // 显示退出提示
        this.showMsg('版本已过期，已自动退出登录');

        // 重置登录表单状态
        this.isLoading = false;
        this.staffIsLoading = false;

      } catch (error) {
        console.error('清除登录数据失败:', error);
      }
    },

    // 跳转到门店端首页
    navigateToHomePage() {
      this.showMsg('门店端登录成功！');

      setTimeout(() => {
        uni.switchTab({
          url: '/pages/home/<USER>',
        });
      }, 1500);
    },
  },
};
</script>

<style lang="scss" scoped>
// 动画关键帧定义
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}



@keyframes slide-up {
  from { transform: translateY(50rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes button-press {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

// 主容器
.modern-login-container {
  min-height: 100vh;
  background: #f8f9fa;
  animation: fade-in 1s ease-out;
}

// 顶部背景区域
.header-background-section {
  position: relative;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 40rpx 40rpx 20rpx;
  }
}

// 左上角入驻申请按钮
.top-left-join-btn {
  position: absolute;
  top: 70rpx;
  left: 40rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  z-index: 10;

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
  }

  .join-btn-text {
    font-size: 26rpx;
    color: #fff;
    font-weight: 500;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  }
}
// 可拖动在线客服按钮样式
.draggable-service-btn {
  position: fixed;
  width: 70px;
  height: 80px;
  border-radius: 20px;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  box-shadow: 0 8px 24px rgba(253, 209, 24, 0.4);
  z-index: 999;
  transition: transform 0.2s ease;
  padding: 8px 6px;

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e6c015 0%, #e6721a 100%);
    box-shadow: 0 4px 16px rgba(253, 209, 24, 0.5);
  }

  .service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .service-text {
    font-size: 10px;
    color: #fff;
    font-weight: 500;
    text-align: center;
    line-height: 1;
    white-space: nowrap;
  }
}





// 内容包装器
.content-wrapper {
  padding: 0 32rpx 24rpx;
  display: flex;
  flex-direction: column;
}

// 身份切换器
.role-switcher {
  margin-bottom: 24rpx;
  animation: slide-up 1s ease-out 0.3s both;
}

.role-tabs {
  display: flex;
  background: #fff;
  border-radius: 20rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.role-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  gap: 12rpx;

  &.active {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 20rpx rgba(253, 209, 24, 0.4);
  }

  &:active {
    transform: scale(0.98);
  }
}

.role-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: rgba(253, 209, 24, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;

  .role-tab.active & {
    background: rgba(255, 255, 255, 0.2);
  }
}

.role-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;

  .role-tab.active & {
    color: #fff;
    font-weight: 600;
  }
}

// 身份选择区域
.role-selector {
  margin-bottom: 30rpx;
  animation: slide-up 1s ease-out 0.3s both;
}

.role-title {
  text-align: center;
  margin-bottom: 30rpx;

  text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.role-cards {
  display: flex;
  gap: 20rpx;
}

.role-card {
  flex: 1;
  position: relative;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-color: #fdd118;
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 24rpx rgba(253, 209, 24, 0.3);
  }

  &:active {
    transform: scale(0.98);
  }
}

.role-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.role-info {
  text-align: center;
}

.role-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  transition: color 0.3s ease;

  .role-card.active & {
    color: #fff;
  }
}

.role-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  transition: color 0.3s ease;

  .role-card.active & {
    color: rgba(255, 255, 255, 0.9);
  }
}

.role-check {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
}

// Logo区域
.logo-section {
  text-align: center;
  margin-bottom: 20rpx;
  animation: slide-up 1s ease-out 0.2s both;
}

.logo-container {
  display: inline-block;
  margin-bottom: 16rpx;
}

.logo-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.brand-text {
  .brand-title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    margin-bottom: 6rpx;
  }

  .brand-subtitle {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  }
}

// 登录卡片
.login-card {
  position: relative;
  background: #fff;
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  animation: slide-up 1s ease-out 0.4s both;

  &.staff-login-card {
    animation: slide-up 1s ease-out 0.6s both;
  }
}

// 卡片标题
.card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: rgba(253, 209, 24, 0.1);
  border-radius: 50%;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-glow {
  display: none;
}

// 现代化Tab切换
.modern-tabs {
  position: relative;
  display: flex;
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
}

.tab-slider {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  border-radius: 16rpx;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.4);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;

  text {
    font-size: 30rpx;
    font-weight: 500;
    color: #666;
    transition: color 0.3s ease;
  }

  &.active text {
    color: #fff;
    font-weight: 600;
  }
}

// 表单区域
.form-section {
  .input-group {
    margin-bottom: 24rpx;
  }
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &.focused {
    background: #fff;
    border-color: #fdd118;
    box-shadow: 0 0 0 6rpx rgba(253, 209, 24, 0.1);
  }
}

.input-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.modern-input {
  width: 100%;
  height: 100rpx;
  padding: 0 140rpx 0 70rpx; /* 增加右侧padding为按钮留出空间 */
  border: none;
  background: transparent;
  font-size: 32rpx;
  color: #333;

  &::placeholder {
    color: #999;
    font-size: 30rpx;
  }
}

.input-suffix {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 10; /* 确保按钮在输入框之上 */
  pointer-events: auto; /* 确保按钮可以接收点击事件 */
}

.password-toggle {
  padding: 20rpx; /* 增加padding提供更大的点击区域 */
  margin: -20rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80rpx; /* 增加最小宽度 */
  min-height: 80rpx; /* 增加最小高度 */
  z-index: 15; /* 确保在最上层 */
  pointer-events: auto;

  &:active {
    background-color: rgba(253, 209, 24, 0.1);
    transform: scale(0.9);
  }
}



.code-btn {
  padding: 16rpx 24rpx; /* 增加padding提供更大的点击区域 */
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 120rpx; /* 设置最小宽度 */
  min-height: 60rpx; /* 设置最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15; /* 确保在最上层 */
  pointer-events: auto;

  &.disabled {
    background: #ccc;
    color: #999;
  }

  &:not(.disabled):active {
    transform: scale(0.95);
  }
}

// 记住密码区域
.remember-section {
  margin: 16rpx 0 8rpx 0;
}

.remember-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.remember-text {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.4;
}

// 协议区域
.agreement-section {
  margin: 8rpx 0 20rpx 0;
}

.agreement-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.custom-checkbox {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 3rpx solid rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &.checked {
    background: #fff;
    border-color: #fdd118;
    transform: scale(1.05);
  }
}

.agreement-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4rpx;
  line-height: 1.3;
}

.agreement-text {
  font-size: 22rpx;
  color: rgba(0, 0, 0, 0.7);
  line-height: 1.3;
}

.agreement-link {
  font-size: 22rpx;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
  text-decoration: underline;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    opacity: 0.6;
  }
}

// 登录按钮
.login-btn-wrapper {
  margin: 32rpx 0 24rpx;
}

.modern-login-btn {
  position: relative;
  height: 100rpx;
  border-radius: 25rpx;
  overflow: hidden;
  transition: all 0.3s ease;

  &:not(.disabled):active {
    animation: button-press 0.2s ease;
  }

  &.disabled {
    opacity: 0.6;
    transform: none !important;
  }

  &.loading {
    pointer-events: none;
  }
}

.btn-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shine 2s ease infinite;
  }
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.btn-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;

  text {
    font-size: 36rpx;
    font-weight: 600;
    color: #fff;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  }
}

// 底部操作
.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.action-link {
  font-size: 28rpx;
  color: #666;
  transition: color 0.3s ease;

  &:active {
    color: #fdd118;
  }
}

.register-link {
  .highlight {
    color: #ff801b;
    font-weight: 500;
  }
}

// 微信登录区域
.wechat-login-section {
  margin-top: 24rpx;
  padding-top: 20rpx;
}

// 第三方登录
.social-login {
  margin-top: auto;
  padding-top: 40rpx;
  animation: slide-up 1s ease-out 0.6s both;
}

.divider-line {
  position: relative;
  text-align: center;
  margin-bottom: 40rpx;

  .divider-text {
    font-size: 28rpx;
    color: #666;
    background: #f8f9fa;
    padding: 0 30rpx;
    border-radius: 20rpx;
    display: inline-block;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1rpx;
    background: #e0e0e0;
    z-index: -1;
  }
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }
}

.social-icon {
  margin-right: 16rpx;
}

.social-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 员工端手机号授权按钮特殊样式 */
button.social-btn {
  border: none;
  padding: 24rpx;
  height: auto;
  line-height: normal;

  &:disabled {
    opacity: 0.7;
  }

  &::after {
    border: none;
  }
}


</style>

